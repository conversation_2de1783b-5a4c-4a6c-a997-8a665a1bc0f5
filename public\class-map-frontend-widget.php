<?php
/**
 * Frontend widget class
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Frontend Widget class for My Accessibility Plugin
 *
 * @since 1.0.0
 */
class MAP_Frontend_Widget {

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize frontend widget
     *
     * @since 1.0.0
     */
    private function init() {
        // Add hooks
        add_action('wp_footer', array($this, 'render_widget'));
        add_action('wp_head', array($this, 'add_widget_styles'));
    }

    /**
     * Render the accessibility widget
     *
     * @since 1.0.0
     */
    public function render_widget() {
        // Get settings with fallback
        try {
            // Check if MAP_Core class exists and can be instantiated
            if (class_exists('MAP_Core')) {
                $settings = MAP_Core::get_instance()->get_settings();
            } else {
                throw new Exception('MAP_Core class not found');
            }
        } catch (Exception $e) {
            // Fallback to default settings if MAP_Core fails

            // Try direct database query as last resort
            $db_settings = get_option('map_settings', false);
            if ($db_settings && is_array($db_settings)) {
                $settings = wp_parse_args($db_settings, array(
                    'text_to_speech_enabled' => true,
                    'widget_position' => 'bottom-right',
                    'widget_style' => 'modern',
                    'button_size' => 'medium',
                    'button_color' => '#0073aa'
                ));
            } else {
                // Ultimate fallback - always enable
                $settings = array(
                    'text_to_speech_enabled' => true,
                    'widget_position' => 'bottom-right',
                    'widget_style' => 'modern',
                    'button_size' => 'medium',
                    'button_color' => '#0073aa'
                );
            }
        }

        // Ensure settings is an array and has required keys
        if (!is_array($settings)) {
            $settings = array(
                'text_to_speech_enabled' => true,
                'widget_position' => 'bottom-right',
                'widget_style' => 'modern',
                'button_size' => 'medium',
                'button_color' => '#0073aa'
            );
        }

        // Set defaults for missing keys
        $settings = wp_parse_args($settings, array(
            'text_to_speech_enabled' => true,
            'widget_position' => 'bottom-right',
            'widget_style' => 'modern',
            'button_size' => 'medium',
            'button_color' => '#0073aa'
        ));

        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        $position_class = 'map-position-' . str_replace('_', '-', $settings['widget_position']);
        $style_class = 'map-style-' . $settings['widget_style'];
        $size_class = 'map-size-' . $settings['button_size'];
        
        ?>
        <div id="map-accessibility-widget" class="map-accessibility-widget <?php echo esc_attr($position_class . ' ' . $style_class . ' ' . $size_class); ?>" role="region" aria-label="<?php esc_attr_e('Accessibility Tools', MAP_TEXT_DOMAIN); ?>">
            
            <!-- Main Toggle Button -->
            <button id="map-main-toggle" class="map-main-toggle" type="button" aria-expanded="false" aria-controls="map-widget-panel" aria-label="<?php esc_attr_e('Open accessibility tools', MAP_TEXT_DOMAIN); ?>">
                <span class="map-toggle-icon" aria-hidden="true">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M12 8C13.1 8 14 8.9 14 10C14 11.1 13.1 12 12 12C10.9 12 10 11.1 10 10C10 8.9 10.9 8 12 8M12 14C13.1 14 14 14.9 14 16C14 17.1 13.1 18 12 18C10.9 18 10 17.1 10 16C10 14.9 10.9 14 12 14M6 8C7.1 8 8 8.9 8 10C8 11.1 7.1 12 6 12C4.9 12 4 11.1 4 10C4 8.9 4.9 8 6 8M18 8C19.1 8 20 8.9 20 10C20 11.1 19.1 12 18 12C16.9 12 16 11.1 16 10C16 8.9 16.9 8 18 8M6 14C7.1 14 8 14.9 8 16C8 17.1 7.1 18 6 18C4.9 18 4 17.1 4 16C4 14.9 4.9 14 6 14M18 14C19.1 14 20 14.9 20 16C20 17.1 19.1 18 18 18C16.9 18 16 17.1 16 16C16 14.9 16.9 14 18 14"/>
                    </svg>
                </span>
            </button>
            
            <!-- Widget Panel -->
            <div id="map-widget-panel" class="map-widget-panel" style="display: none;" aria-hidden="true">
                <div class="map-panel-header">
                    <!-- Main title - shown when on main menu -->
                    <h3 id="map-panel-title" class="map-panel-title"><?php esc_html_e('Accessibility Tools', MAP_TEXT_DOMAIN); ?></h3>

                    <!-- Back button - shown when in category views -->
                    <button id="map-header-back-button" class="map-header-back-button" type="button" aria-label="<?php esc_attr_e('Back to main menu', MAP_TEXT_DOMAIN); ?>" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                        </svg>
                        <span id="map-header-back-text"><?php esc_html_e('Back', MAP_TEXT_DOMAIN); ?></span>
                    </button>

                    <div class="map-header-buttons">
                        <button id="map-reset-category" class="map-reset-button" type="button" aria-label="<?php esc_attr_e('Reset category options to default', MAP_TEXT_DOMAIN); ?>" style="display: none;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                            </svg>
                            <span><?php esc_html_e('Reset', MAP_TEXT_DOMAIN); ?></span>
                        </button>
                        <button id="map-close-panel" class="map-close-button" type="button" aria-label="<?php esc_attr_e('Close accessibility tools', MAP_TEXT_DOMAIN); ?>">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="map-panel-content">
                    <!-- Premium Animation Container for Modal Views -->
                    <div class="map-modal-views-container">
                        <!-- Main Menu (Level 1) - Category Selection -->
                    <div id="map-main-menu" class="map-modal-view map-modal-view-active" role="menu" aria-label="<?php esc_attr_e('Accessibility categories', MAP_TEXT_DOMAIN); ?>">
                        <div class="map-category-grid">
                            <!-- Text Category -->
                            <button id="map-category-text" class="map-category-button" type="button" role="menuitem" data-category="text" aria-describedby="map-category-text-desc">
                                <div class="map-category-icon map-category-icon-text">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                        <polyline points="14,2 14,8 20,8"/>
                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                        <polyline points="10,9 9,9 8,9"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title"><?php esc_html_e('Text', MAP_TEXT_DOMAIN); ?></div>
                                    <div id="map-category-text-desc" class="map-category-desc"><?php esc_html_e('Reading and text options', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>

                            <!-- Colors Category -->
                            <button id="map-category-colors" class="map-category-button" type="button" role="menuitem" data-category="colors" aria-describedby="map-category-colors-desc">
                                <div class="map-category-icon map-category-icon-colors">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="13.5" cy="6.5" r=".5"/>
                                        <circle cx="17.5" cy="10.5" r=".5"/>
                                        <circle cx="8.5" cy="7.5" r=".5"/>
                                        <circle cx="6.5" cy="12.5" r=".5"/>
                                        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title"><?php esc_html_e('Contrast & Colors', MAP_TEXT_DOMAIN); ?></div>
                                    <div id="map-category-colors-desc" class="map-category-desc"><?php esc_html_e('Contrast and color themes', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>
                            <!-- Navigation Category -->
                            <button id="map-category-navigation" class="map-category-button" type="button" role="menuitem" data-category="navigation" aria-describedby="map-category-navigation-desc">
                                <div class="map-category-icon map-category-icon-navigation">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect x="2" y="4" width="20" height="16" rx="2"/>
                                        <path d="M6 8h.01"/>
                                        <path d="M10 8h.01"/>
                                        <path d="M14 8h.01"/>
                                        <path d="M18 8h.01"/>
                                        <path d="M8 12h.01"/>
                                        <path d="M12 12h.01"/>
                                        <path d="M16 12h.01"/>
                                        <path d="M7 16h10"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title"><?php esc_html_e('Navigation', MAP_TEXT_DOMAIN); ?></div>
                                    <div id="map-category-navigation-desc" class="map-category-desc"><?php esc_html_e('Keyboard shortcuts and navigation aids', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>
                            <!-- Preferences Category -->
                            <button id="map-category-preferences" class="map-category-button" type="button" role="menuitem" data-category="preferences" aria-describedby="map-category-preferences-desc">
                                <div class="map-category-icon map-category-icon-preferences">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="3"/>
                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                    </svg>
                                </div>
                                <div class="map-category-content">
                                    <div class="map-category-title"><?php esc_html_e('Preferences', MAP_TEXT_DOMAIN); ?></div>
                                    <div id="map-category-preferences-desc" class="map-category-desc"><?php esc_html_e('General settings and preferences', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                                <div class="map-category-arrow" aria-hidden="true">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Text Category View (Level 2) -->
                    <div id="map-view-text" class="map-modal-view" role="region" aria-label="<?php esc_attr_e('Text accessibility options', MAP_TEXT_DOMAIN); ?>">
                        <div class="map-view-header">
                            <h4 class="map-view-title"><?php esc_html_e('Text Options', MAP_TEXT_DOMAIN); ?></h4>
                        </div>

                        <div class="map-view-content">
                            <!-- Text-to-Speech Section -->
                            <div class="map-feature-section">
                                <button id="map-tts-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Text to Speech', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Select text to hear it read aloud', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-toggle-switch">
                                        <span class="map-toggle-slider"></span>
                                    </div>
                                </button>


                            </div>

                            <!-- Dyslexic Font Toggle Section -->
                            <div class="map-feature-section">
                                <button id="map-dyslexic-font-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                            <!-- Clean "D" letter only -->
                                            <path d="M4 3h8c5.5 0 10 4.5 10 10s-4.5 10-10 10H4V3zm3 3v14h5c3.9 0 7-3.1 7-7s-3.1-7-7-7H7z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Enable Dyslexic Font', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Apply dyslexia-friendly font to improve readability', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-toggle-switch">
                                        <span class="map-toggle-slider"></span>
                                    </div>
                                </button>


                            </div>

                            <!-- Reading Guide Section -->
                            <div class="map-feature-section">
                                <button id="map-reading-guide-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <g transform="rotate(30 12 12)">
                                                <!-- Main ruler body -->
                                                <rect x="2" y="8" width="20" height="8" fill="none" stroke="currentColor" stroke-width="2"/>

                                                <!-- Measurement marks along bottom edge -->
                                                <line x1="4" y1="16" x2="4" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                                <line x1="5.5" y1="16" x2="5.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                                <line x1="7" y1="16" x2="7" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                                <line x1="8.5" y1="16" x2="8.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                                <line x1="10" y1="16" x2="10" y2="13.5" stroke="currentColor" stroke-width="2"/>
                                                <line x1="11.5" y1="16" x2="11.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                                <line x1="13" y1="16" x2="13" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                                <line x1="14.5" y1="16" x2="14.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                                <line x1="16" y1="16" x2="16" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                                <line x1="17.5" y1="16" x2="17.5" y2="14.5" stroke="currentColor" stroke-width="1"/>
                                                <line x1="19" y1="16" x2="19" y2="14" stroke="currentColor" stroke-width="1.5"/>
                                                <line x1="20.5" y1="16" x2="20.5" y2="14.5" stroke="currentColor" stroke-width="1"/>

                                                <!-- Circle detail in upper right -->
                                                <circle cx="18" cy="10.5" r="1.5" fill="none" stroke="currentColor" stroke-width="1.5"/>
                                                <circle cx="18" cy="10.5" r="0.5" fill="currentColor"/>
                                            </g>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Reading Guide', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Show a horizontal line that follows your mouse to help focus on text', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-toggle-switch">
                                        <span class="map-toggle-slider"></span>
                                    </div>
                                </button>


                            </div>

                            <!-- Font Size Control Section -->
                            <div class="map-feature-section">
                                <button id="map-font-size-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                            <!-- Large T letter (main) -->
                                            <path d="M8 2h14v4h-5v16h-4V6H8V2z"/>

                                            <!-- Small T letter (positioned lower left) -->
                                            <path d="M2 12h8v2.5H7v7.5H5v-7.5H2V12z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Font Size', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Adjust text size for better readability', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-feature-status">
                                        <span id="map-font-size-value">Default</span>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-font-size-controls" class="map-feature-controls" style="display: none;">

                                    <div class="map-font-size-controls">
                                        <div class="map-size-control-group">
                                            <button id="map-font-size-decrease" class="map-size-control-btn map-size-decrease" type="button" aria-label="<?php esc_attr_e('Decrease font size', MAP_TEXT_DOMAIN); ?>">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                    <path d="M5 12h14"/>
                                                </svg>
                                            </button>

                                            <div class="map-size-indicator">
                                                <div class="map-size-preview">Aa</div>
                                                <div class="map-size-percentage" id="map-font-percentage">100%</div>
                                            </div>

                                            <button id="map-font-size-increase" class="map-size-control-btn map-size-increase" type="button" aria-label="<?php esc_attr_e('Increase font size', MAP_TEXT_DOMAIN); ?>">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round">
                                                    <path d="M12 5v14M5 12h14"/>
                                                </svg>
                                            </button>
                                        </div>

                                        <button id="map-font-size-reset" class="map-control-reset" type="button" aria-label="<?php esc_attr_e('Reset font size to default', MAP_TEXT_DOMAIN); ?>">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                                <path d="M21 3v5h-5"/>
                                                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                                <path d="M3 21v-5h5"/>
                                            </svg>
                                            <?php esc_html_e('Reset to Default', MAP_TEXT_DOMAIN); ?>
                                        </button>
                                    </div>
                                </div>

                                <div id="map-font-size-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">ℹ️</div>
                                    <div class="map-status-text"><?php esc_html_e('Font size has been adjusted. The change applies to all text on the website.', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                            </div>

                            <!-- Line Spacing Control Section -->
                            <div class="map-feature-section">
                                <button id="map-line-spacing-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M6 7h12v2H6V7zm0 4h12v2H6v-2zm0 4h12v2H6v-2z"/>
                                            <path d="M3 4h2v16H3V4z"/>
                                            <path d="M19 4h2v16h-2V4z"/>
                                            <circle cx="4" cy="6" r="1" fill="currentColor"/>
                                            <circle cx="4" cy="12" r="1" fill="currentColor"/>
                                            <circle cx="4" cy="18" r="1" fill="currentColor"/>
                                            <circle cx="20" cy="6" r="1" fill="currentColor"/>
                                            <circle cx="20" cy="12" r="1" fill="currentColor"/>
                                            <circle cx="20" cy="18" r="1" fill="currentColor"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Line Spacing', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Adjust space between lines for better readability', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-feature-status">
                                        <span id="map-line-spacing-value">Default</span>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-line-spacing-controls" class="map-feature-controls" style="display: none;">

                                    <div class="map-line-spacing-controls">
                                        <div class="map-spacing-control-group">
                                            <div class="map-spacing-labels">
                                                <span class="map-spacing-label-min">Tight</span>
                                                <span class="map-spacing-label-center">Normal</span>
                                                <span class="map-spacing-label-max">Wide</span>
                                            </div>

                                            <div class="map-slider-container">
                                                <input type="range"
                                                       id="map-line-spacing-slider"
                                                       class="map-premium-slider"
                                                       min="1.0"
                                                       max="2.5"
                                                       step="0.1"
                                                       value="1.5"
                                                       aria-label="<?php esc_attr_e('Adjust line spacing', MAP_TEXT_DOMAIN); ?>">
                                                <div class="map-slider-track">
                                                    <div class="map-slider-progress" id="map-slider-progress"></div>
                                                </div>
                                            </div>

                                            <div class="map-spacing-value">
                                                <span id="map-spacing-numeric">1.5x</span>
                                            </div>
                                        </div>

                                        <button id="map-line-spacing-reset" class="map-control-reset" type="button" aria-label="<?php esc_attr_e('Reset line spacing to default', MAP_TEXT_DOMAIN); ?>">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                                <path d="M21 3v5h-5"/>
                                                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                                <path d="M3 21v-5h5"/>
                                            </svg>
                                            <?php esc_html_e('Reset to Default', MAP_TEXT_DOMAIN); ?>
                                        </button>
                                    </div>
                                </div>

                                <div id="map-line-spacing-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">ℹ️</div>
                                    <div class="map-status-text"><?php esc_html_e('Line spacing has been adjusted. The change applies to all text on the website.', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                            </div>


                        </div>
                    </div>

                    <!-- Navigation Category View (Level 2) -->
                    <div id="map-view-navigation" class="map-modal-view" role="region" aria-label="<?php esc_attr_e('Navigation accessibility options', MAP_TEXT_DOMAIN); ?>">
                        <div class="map-view-header">
                            <h4 class="map-view-title"><?php esc_html_e('Navigation Options', MAP_TEXT_DOMAIN); ?></h4>
                        </div>

                        <div class="map-view-content">

                            <!-- ADHD Focus Mode Section -->
                            <div class="map-feature-section">
                                <button id="map-nav-adhd-focus-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                            <!-- Outer focus ring - represents attention boundary -->
                                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.4"/>

                                            <!-- Middle focus ring - shows concentration layers -->
                                            <circle cx="12" cy="12" r="7" stroke="currentColor" stroke-width="2" fill="none" opacity="0.7"/>

                                            <!-- Inner focus center - the main attention point -->
                                            <circle cx="12" cy="12" r="4" fill="currentColor"/>

                                            <!-- Central focus dot - precise attention point -->
                                            <circle cx="12" cy="12" r="1.5" fill="white"/>

                                            <!-- Focus beam indicators - showing directed attention -->
                                            <path d="M12 2v4M12 18v4M2 12h4M18 12h4" stroke="currentColor" stroke-width="2" opacity="0.6"/>

                                            <!-- Corner focus markers - premium detail -->
                                            <path d="M4 4l2 2M4 20l2-2M20 4l-2 2M20 20l-2-2" stroke="currentColor" stroke-width="1.5" opacity="0.5"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('ADHD Focus Mode', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Reduce distractions and highlight content for better focus', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-toggle-switch">
                                        <span class="map-toggle-slider"></span>
                                    </div>
                                </button>

                                <div id="map-nav-adhd-focus-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">🎯</div>
                                    <div class="map-status-text"><?php esc_html_e('ADHD Focus Mode is active. Content is highlighted and distractions are minimized.', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                            </div>

                            <!-- Big Cursor Section -->
                            <div class="map-feature-section">
                                <button id="map-big-cursor-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <!-- Blue rounded background -->
                                            <rect x="2" y="2" width="20" height="20" rx="6" fill="#6366F1"/>

                                            <!-- White arrow cursor inside -->
                                            <path d="M7 7v10l3-3h3l-3-3V7z" fill="#FFFFFF" stroke="none"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Big Cursor', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Enlarge cursor size for better visibility and easier tracking', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-toggle-switch">
                                        <span class="map-toggle-slider"></span>
                                    </div>
                                </button>

                                <div id="map-big-cursor-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">🖱️</div>
                                    <div class="map-status-text"><?php esc_html_e('Big Cursor is active. Cursor size has been enlarged for better visibility.', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                            </div>


                        </div>
                    </div>

                    <!-- Placeholder views for other categories -->
                    <div id="map-view-colors" class="map-modal-view" role="region" aria-label="<?php esc_attr_e('Color accessibility options', MAP_TEXT_DOMAIN); ?>">
                        <div class="map-view-header">
                            <h4 class="map-view-title"><?php esc_html_e('Contrast & Colors', MAP_TEXT_DOMAIN); ?></h4>
                        </div>
                        <div class="map-view-content">
                            <!-- Visual Themes Section - Matching Text Category Style -->
                            <div class="map-feature-section">
                                <button id="map-contrast-themes-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                                            <path d="M12 6c-3.31 0-6 2.69-6 6 0 1.66.67 3.16 1.76 4.24l8.48-8.48C15.16 6.67 13.66 6 12 6z"/>
                                            <path d="M18 12c0 1.66-.67 3.16-1.76 4.24l-8.48-8.48C8.84 6.67 10.34 6 12 6c3.31 0 6 2.69 6 6z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Visual Themes', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Choose a visual style that works best for you', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>

                                </button>

                                <div id="map-contrast-themes-content" class="map-feature-controls" style="display: none;">
                                    <!-- Theme Selector Container - Clean, minimal design -->
                                    <div class="map-theme-selector">

                                        <!-- Theme Preview Card -->
                                        <div class="map-theme-preview-card">
                                            <!-- Navigation Arrows -->
                                            <button id="map-theme-prev" class="map-theme-nav map-theme-nav-prev" type="button" aria-label="<?php esc_attr_e('Previous theme (applies immediately)', MAP_TEXT_DOMAIN); ?>">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/>
                                                </svg>
                                            </button>

                                            <button id="map-theme-next" class="map-theme-nav map-theme-nav-next" type="button" aria-label="<?php esc_attr_e('Next theme (applies immediately)', MAP_TEXT_DOMAIN); ?>">
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                                                </svg>
                                            </button>

                                            <!-- Theme Icon Preview -->
                                            <div class="map-theme-icon-preview-container">
                                                <div id="map-theme-icon-preview" class="map-theme-icon-preview" data-theme="normal">
                                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                                                    </svg>
                                                </div>
                                            </div>

                                            <!-- Theme Info -->
                                            <div class="map-theme-info">
                                                <h4 id="map-theme-name" class="map-theme-title">Default</h4>
                                            </div>
                                        </div>

                                        <!-- Theme Dots Indicator -->
                                        <div class="map-theme-dots">
                                            <button class="map-theme-dot active" data-theme="normal" aria-label="<?php esc_attr_e('Apply default theme', MAP_TEXT_DOMAIN); ?>"></button>
                                            <button class="map-theme-dot" data-theme="monochrome" aria-label="<?php esc_attr_e('Monochrome theme', MAP_TEXT_DOMAIN); ?>"></button>
                                            <button class="map-theme-dot" data-theme="low-saturation" aria-label="<?php esc_attr_e('Low saturation theme', MAP_TEXT_DOMAIN); ?>"></button>
                                            <button class="map-theme-dot" data-theme="high-saturation" aria-label="<?php esc_attr_e('High saturation theme', MAP_TEXT_DOMAIN); ?>"></button>
                                            <button class="map-theme-dot" data-theme="dark" aria-label="<?php esc_attr_e('Dark theme', MAP_TEXT_DOMAIN); ?>"></button>
                                            <button class="map-theme-dot" data-theme="high-contrast" aria-label="<?php esc_attr_e('High contrast theme', MAP_TEXT_DOMAIN); ?>"></button>
                                            <button class="map-theme-dot" data-theme="sepia" aria-label="<?php esc_attr_e('Sepia theme', MAP_TEXT_DOMAIN); ?>"></button>
                                            <button class="map-theme-dot" data-theme="colorblind" aria-label="<?php esc_attr_e('Color blind friendly theme', MAP_TEXT_DOMAIN); ?>"></button>
                                        </div>


                                    </div>



                                    <div id="map-contrast-theme-status" class="map-status-message" style="display: none;">
                                        <div class="map-status-icon">🎨</div>
                                        <div class="map-status-text"><?php esc_html_e('Theme applied successfully. The visual appearance of the website has been updated.', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Studio Section - Matching Text Category Style -->
                            <div class="map-feature-section">
                                <button id="map-custom-theme-toggle" class="map-feature-toggle" data-active="false" type="button">
                                    <div class="map-feature-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="13.5" cy="6.5" r=".5"/>
                                            <circle cx="17.5" cy="10.5" r=".5"/>
                                            <circle cx="8.5" cy="7.5" r=".5"/>
                                            <circle cx="6.5" cy="12.5" r=".5"/>
                                            <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                                        </svg>
                                    </div>
                                    <div class="map-feature-content">
                                        <div class="map-feature-title"><?php esc_html_e('Color Studio', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Design your perfect color palette with live preview', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-category-arrow">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </button>

                                <div id="map-custom-theme-content" class="map-feature-controls" style="display: none;">
                                    <!-- Premium Compact Color Studio Layout -->
                                    <div class="map-color-studio-compact">

                                        <!-- Text Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                        <polyline points="14,2 14,8 20,8"/>
                                                        <line x1="16" y1="13" x2="8" y2="13"/>
                                                        <line x1="16" y1="17" x2="8" y2="17"/>
                                                        <polyline points="10,9 9,9 8,9"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title"><?php esc_html_e('Text', MAP_TEXT_DOMAIN); ?></span>
                                                    <span class="map-color-desc"><?php esc_html_e('Body text & paragraphs', MAP_TEXT_DOMAIN); ?></span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-text-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-text-color" aria-label="<?php esc_attr_e('Reset text color', MAP_TEXT_DOMAIN); ?>" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Background Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                                        <circle cx="8.5" cy="8.5" r="1.5"/>
                                                        <polyline points="21,15 16,10 5,21"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title"><?php esc_html_e('Background', MAP_TEXT_DOMAIN); ?></span>
                                                    <span class="map-color-desc"><?php esc_html_e('Page & content areas', MAP_TEXT_DOMAIN); ?></span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-bg-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-bg-color" aria-label="<?php esc_attr_e('Reset background color', MAP_TEXT_DOMAIN); ?>" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Links Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title"><?php esc_html_e('Links', MAP_TEXT_DOMAIN); ?></span>
                                                    <span class="map-color-desc"><?php esc_html_e('Hyperlinks & buttons', MAP_TEXT_DOMAIN); ?></span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-link-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-link-color" aria-label="<?php esc_attr_e('Reset link color', MAP_TEXT_DOMAIN); ?>" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Headings Color Row -->
                                        <div class="map-color-row">
                                            <div class="map-color-label">
                                                <div class="map-color-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M6 12h12"/>
                                                        <path d="M6 20V4"/>
                                                        <path d="M18 20V4"/>
                                                    </svg>
                                                </div>
                                                <div class="map-color-info">
                                                    <span class="map-color-title"><?php esc_html_e('Headings', MAP_TEXT_DOMAIN); ?></span>
                                                    <span class="map-color-desc"><?php esc_html_e('Titles & headers', MAP_TEXT_DOMAIN); ?></span>
                                                </div>
                                            </div>
                                            <div class="map-color-controls">
                                                <div class="map-color-picker-wrapper">
                                                    <input type="color" id="map-custom-heading-color" class="map-color-picker-compact" value="">
                                                    <button type="button" class="map-color-reset-btn" data-target="map-custom-heading-color" aria-label="<?php esc_attr_e('Reset heading color', MAP_TEXT_DOMAIN); ?>" style="display: none;">
                                                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M18 6L6 18"/>
                                                            <path d="M6 6l12 12"/>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>



                                    <div id="map-custom-theme-status" class="map-status-message" style="display: none;">
                                        <div class="map-status-icon">✨</div>
                                        <div class="map-status-text"><?php esc_html_e('Custom theme applied successfully!', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <div id="map-view-preferences" class="map-modal-view" role="region" aria-label="<?php esc_attr_e('General preferences', MAP_TEXT_DOMAIN); ?>">
                        <div class="map-view-header">
                            <h4 class="map-view-title"><?php esc_html_e('Preferences', MAP_TEXT_DOMAIN); ?></h4>
                        </div>
                        <div class="map-view-content">
                            <!-- Widget Position Section -->
                            <div class="map-feature-section">
                                <div class="map-feature-header">
                                    <div class="map-feature-icon">📍</div>
                                    <div class="map-feature-info">
                                        <div class="map-feature-title"><?php esc_html_e('Widget Position', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Choose where the accessibility button appears', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                </div>
                                <div class="map-feature-content">
                                    <div class="map-position-grid">
                                        <button class="map-position-option active" data-position="bottom-right" type="button">
                                            <span class="map-position-icon">↘️</span>
                                            <span class="map-position-label"><?php esc_html_e('Bottom Right', MAP_TEXT_DOMAIN); ?></span>
                                        </button>
                                        <button class="map-position-option" data-position="bottom-left" type="button">
                                            <span class="map-position-icon">↙️</span>
                                            <span class="map-position-label"><?php esc_html_e('Bottom Left', MAP_TEXT_DOMAIN); ?></span>
                                        </button>
                                        <button class="map-position-option" data-position="top-right" type="button">
                                            <span class="map-position-icon">↗️</span>
                                            <span class="map-position-label"><?php esc_html_e('Top Right', MAP_TEXT_DOMAIN); ?></span>
                                        </button>
                                        <button class="map-position-option" data-position="top-left" type="button">
                                            <span class="map-position-icon">↖️</span>
                                            <span class="map-position-label"><?php esc_html_e('Top Left', MAP_TEXT_DOMAIN); ?></span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Auto-Save Section -->
                            <div class="map-feature-section">
                                <div class="map-feature-header">
                                    <div class="map-feature-icon">💾</div>
                                    <div class="map-feature-info">
                                        <div class="map-feature-title"><?php esc_html_e('Auto-Save Settings', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Automatically remember your accessibility preferences', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-feature-toggle">
                                        <button id="map-auto-save-toggle" class="map-toggle-button" type="button" data-active="true" aria-pressed="true" aria-describedby="map-auto-save-desc">
                                            <span class="map-toggle-slider"></span>
                                        </button>
                                    </div>
                                </div>
                                <div id="map-auto-save-status" class="map-status-message" style="display: none;">
                                    <div class="map-status-icon">💾</div>
                                    <div class="map-status-text"><?php esc_html_e('Auto-save preferences updated!', MAP_TEXT_DOMAIN); ?></div>
                                </div>
                            </div>

                            <!-- Reset All Settings -->
                            <div class="map-feature-section">
                                <div class="map-feature-header">
                                    <div class="map-feature-icon">🔄</div>
                                    <div class="map-feature-info">
                                        <div class="map-feature-title"><?php esc_html_e('Reset All Settings', MAP_TEXT_DOMAIN); ?></div>
                                        <div class="map-feature-desc"><?php esc_html_e('Clear all saved preferences and return to defaults', MAP_TEXT_DOMAIN); ?></div>
                                    </div>
                                    <div class="map-feature-action">
                                        <button id="map-reset-all-settings" class="map-action-button map-danger-button" type="button">
                                            <span><?php esc_html_e('Reset All', MAP_TEXT_DOMAIN); ?></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div> <!-- Close map-modal-views-container -->
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Add widget-specific styles to head
     *
     * @since 1.0.0
     */
    public function add_widget_styles() {
        // Get settings with fallback
        try {
            // Check if MAP_Core class exists and can be instantiated
            if (class_exists('MAP_Core')) {
                $settings = MAP_Core::get_instance()->get_settings();
            } else {
                throw new Exception('MAP_Core class not found');
            }
        } catch (Exception $e) {
            // Fallback to default settings if MAP_Core fails

            // Try direct database query as last resort
            $db_settings = get_option('map_settings', false);
            if ($db_settings && is_array($db_settings)) {
                $settings = wp_parse_args($db_settings, array(
                    'text_to_speech_enabled' => true,
                    'button_color' => '#0073aa'
                ));
            } else {
                // Ultimate fallback - always enable
                $settings = array(
                    'text_to_speech_enabled' => true,
                    'button_color' => '#0073aa'
                );
            }
        }

        // Ensure settings is an array and has required keys
        if (!is_array($settings)) {
            $settings = array(
                'text_to_speech_enabled' => true,
                'button_color' => '#0073aa'
            );
        }

        // Set defaults for missing keys
        $settings = wp_parse_args($settings, array(
            'text_to_speech_enabled' => true,
            'button_color' => '#0073aa'
        ));

        if (!$settings['text_to_speech_enabled']) {
            return;
        }
        
        ?>
        <style id="map-widget-custom-styles">
            :root {
                --map-primary-color: <?php echo esc_attr($settings['button_color']); ?>;
                --map-primary-hover: <?php echo esc_attr($this->adjust_color_brightness($settings['button_color'], -20)); ?>;
                --map-primary-active: <?php echo esc_attr($this->adjust_color_brightness($settings['button_color'], -40)); ?>;
            }
        </style>
        <?php
    }

    /**
     * Adjust color brightness
     *
     * @param string $hex_color
     * @param int $percent
     * @return string
     * @since 1.0.0
     */
    private function adjust_color_brightness($hex_color, $percent) {
        $hex_color = ltrim($hex_color, '#');
        
        if (strlen($hex_color) == 3) {
            $hex_color = str_repeat(substr($hex_color, 0, 1), 2) . str_repeat(substr($hex_color, 1, 1), 2) . str_repeat(substr($hex_color, 2, 1), 2);
        }
        
        $r = hexdec(substr($hex_color, 0, 2));
        $g = hexdec(substr($hex_color, 2, 2));
        $b = hexdec(substr($hex_color, 4, 2));
        
        $r = max(0, min(255, $r + ($r * $percent / 100)));
        $g = max(0, min(255, $g + ($g * $percent / 100)));
        $b = max(0, min(255, $b + ($b * $percent / 100)));
        
        return '#' . str_pad(dechex($r), 2, '0', STR_PAD_LEFT) . str_pad(dechex($g), 2, '0', STR_PAD_LEFT) . str_pad(dechex($b), 2, '0', STR_PAD_LEFT);
    }
}
