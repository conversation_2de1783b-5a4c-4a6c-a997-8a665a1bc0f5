JQMIGRATE: Migrate is installed, version 3.4.1
frontend.js?ver=1.0.0:1047 Font size applied: 100%
frontend.js?ver=1.0.0:1115 Font size state restored: 100%
frontend.js?ver=1.0.0:1193 Line spacing applied: 1.5
frontend.js?ver=1.0.0:1244 Line spacing state restored: 1.5
frontend.js?ver=1.0.0:454 Loading custom theme from localStorage:
frontend.js?ver=1.0.0:455 - savedCustomThemeColors: {"text":"#000000","background":"#000000","link":"#000000","heading":"#000000"}
frontend.js?ver=1.0.0:456 - savedCustomThemeActive: true
frontend.js?ver=1.0.0:461 - Parsed colors: Object
frontend.js?ver=1.0.0:470 Custom theme will be restored with colors: Object
frontend.js?ver=1.0.0:1425 === applyCustomThemeDirectly() CALLED ===
frontend.js?ver=1.0.0:1426 Colors at start of applyCustomThemeDirectly: Object
frontend.js?ver=1.0.0:1434 Colors before generateAndApplyCustomCSS: Object
frontend.js?ver=1.0.0:1535 === GENERATING CSS ===
frontend.js?ver=1.0.0:1536 Current customThemeColors: Object
frontend.js?ver=1.0.0:1537 Background color: #000000 (type: string )
frontend.js?ver=1.0.0:1538 Text color: #000000 (type: string )
frontend.js?ver=1.0.0:1539 Link color: #000000 (type: string )
frontend.js?ver=1.0.0:1540 Heading color: #000000 (type: string )
frontend.js?ver=1.0.0:1545 ⚠️ DETECTED BLACK COLORS - ATTEMPTING TO RESTORE FROM LOCALSTORAGE
frontend.js?ver=1.0.0:1550 Restored colors from localStorage: Object
frontend.js?ver=1.0.0:1564 Adding background color to CSS: #000000
frontend.js?ver=1.0.0:1568 Adding text color to CSS: #000000
frontend.js?ver=1.0.0:1573 Body CSS: body.map-theme-custom { background-color: #000000 !important; color: #000000 !important; }
frontend.js?ver=1.0.0:1626 === FINAL CSS ===
frontend.js?ver=1.0.0:1627 Generated CSS: <style id="map-custom-theme-styles">body.map-theme-custom { background-color: #000000 !important; color: #000000 !important; }
                    body.map-theme-custom p,
                    body.map-theme-custom div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    body.map-theme-custom span,
                    body.map-theme-custom li,
                    body.map-theme-custom td,
                    body.map-theme-custom th {
                        color: #000000 !important;
                    }
                    body.map-theme-custom h1,
                    body.map-theme-custom h2,
                    body.map-theme-custom h3,
                    body.map-theme-custom h4,
                    body.map-theme-custom h5,
                    body.map-theme-custom h6 {
                        color: #000000 !important;
                    }
                    body.map-theme-custom a {
                        color: #000000 !important;
                    }
                    body.map-theme-custom main,
                    body.map-theme-custom article,
                    body.map-theme-custom section,
                    body.map-theme-custom .content,
                    body.map-theme-custom .post,
                    body.map-theme-custom .page {
                        background-color: #000000 !important;
                    }</style>
frontend.js?ver=1.0.0:1635 === CSS APPLIED ===
frontend.js?ver=1.0.0:1636 Custom theme applied with colors: Object
frontend.js?ver=1.0.0:1446 Custom theme applied directly using stored values: Object
frontend.js?ver=1.0.0:1337 Contrast theme state restored: custom
frontend.js?ver=1.0.0:801 Dyslexic font disabled
frontend.js?ver=1.0.0:895 Reading guide disabled
frontend.js?ver=1.0.0:2736 MapAccessibility instance destroyed
frontend.js?ver=1.0.0:1047 Font size applied: 100%
frontend.js?ver=1.0.0:1115 Font size state restored: 100%
frontend.js?ver=1.0.0:1193 Line spacing applied: 1.5
frontend.js?ver=1.0.0:1244 Line spacing state restored: 1.5
frontend.js?ver=1.0.0:454 Loading custom theme from localStorage:
frontend.js?ver=1.0.0:455 - savedCustomThemeColors: {"text":"#000000","background":"#000000","link":"#000000","heading":"#000000"}
frontend.js?ver=1.0.0:456 - savedCustomThemeActive: true
frontend.js?ver=1.0.0:461 - Parsed colors: Object
frontend.js?ver=1.0.0:470 Custom theme will be restored with colors: Object
frontend.js?ver=1.0.0:1425 === applyCustomThemeDirectly() CALLED ===
frontend.js?ver=1.0.0:1426 Colors at start of applyCustomThemeDirectly: Object
frontend.js?ver=1.0.0:1434 Colors before generateAndApplyCustomCSS: Object
frontend.js?ver=1.0.0:1535 === GENERATING CSS ===
frontend.js?ver=1.0.0:1536 Current customThemeColors: Object
frontend.js?ver=1.0.0:1537 Background color: #000000 (type: string )
frontend.js?ver=1.0.0:1538 Text color: #000000 (type: string )
frontend.js?ver=1.0.0:1539 Link color: #000000 (type: string )
frontend.js?ver=1.0.0:1540 Heading color: #000000 (type: string )
frontend.js?ver=1.0.0:1545 ⚠️ DETECTED BLACK COLORS - ATTEMPTING TO RESTORE FROM LOCALSTORAGE
frontend.js?ver=1.0.0:1550 Restored colors from localStorage: Object
frontend.js?ver=1.0.0:1564 Adding background color to CSS: #000000
frontend.js?ver=1.0.0:1568 Adding text color to CSS: #000000
frontend.js?ver=1.0.0:1573 Body CSS: body.map-theme-custom { background-color: #000000 !important; color: #000000 !important; }
frontend.js?ver=1.0.0:1626 === FINAL CSS ===
frontend.js?ver=1.0.0:1627 Generated CSS: <style id="map-custom-theme-styles">body.map-theme-custom { background-color: #000000 !important; color: #000000 !important; }
                    body.map-theme-custom p,
                    body.map-theme-custom div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    body.map-theme-custom span,
                    body.map-theme-custom li,
                    body.map-theme-custom td,
                    body.map-theme-custom th {
                        color: #000000 !important;
                    }
                    body.map-theme-custom h1,
                    body.map-theme-custom h2,
                    body.map-theme-custom h3,
                    body.map-theme-custom h4,
                    body.map-theme-custom h5,
                    body.map-theme-custom h6 {
                        color: #000000 !important;
                    }
                    body.map-theme-custom a {
                        color: #000000 !important;
                    }
                    body.map-theme-custom main,
                    body.map-theme-custom article,
                    body.map-theme-custom section,
                    body.map-theme-custom .content,
                    body.map-theme-custom .post,
                    body.map-theme-custom .page {
                        background-color: #000000 !important;
                    }</style>
frontend.js?ver=1.0.0:1635 === CSS APPLIED ===
frontend.js?ver=1.0.0:1636 Custom theme applied with colors: Object
frontend.js?ver=1.0.0:1446 Custom theme applied directly using stored values: Object
frontend.js?ver=1.0.0:1337 Contrast theme state restored: custom
frontend.js?ver=1.0.0:801 Dyslexic font disabled
frontend.js?ver=1.0.0:895 Reading guide disabled
frontend.js?ver=1.0.0:2736 MapAccessibility instance destroyed
frontend.js?ver=1.0.0:1047 Font size applied: 100%
frontend.js?ver=1.0.0:1115 Font size state restored: 100%
frontend.js?ver=1.0.0:1193 Line spacing applied: 1.5
frontend.js?ver=1.0.0:1244 Line spacing state restored: 1.5
frontend.js?ver=1.0.0:454 Loading custom theme from localStorage:
frontend.js?ver=1.0.0:455 - savedCustomThemeColors: {"text":"#000000","background":"#000000","link":"#000000","heading":"#000000"}
frontend.js?ver=1.0.0:456 - savedCustomThemeActive: true
frontend.js?ver=1.0.0:461 - Parsed colors: Object
frontend.js?ver=1.0.0:470 Custom theme will be restored with colors: Object
frontend.js?ver=1.0.0:1425 === applyCustomThemeDirectly() CALLED ===
frontend.js?ver=1.0.0:1426 Colors at start of applyCustomThemeDirectly: Object
frontend.js?ver=1.0.0:1434 Colors before generateAndApplyCustomCSS: Object
frontend.js?ver=1.0.0:1535 === GENERATING CSS ===
frontend.js?ver=1.0.0:1536 Current customThemeColors: Object
frontend.js?ver=1.0.0:1537 Background color: #000000 (type: string )
frontend.js?ver=1.0.0:1538 Text color: #000000 (type: string )
frontend.js?ver=1.0.0:1539 Link color: #000000 (type: string )
frontend.js?ver=1.0.0:1540 Heading color: #000000 (type: string )
frontend.js?ver=1.0.0:1545 ⚠️ DETECTED BLACK COLORS - ATTEMPTING TO RESTORE FROM LOCALSTORAGE
frontend.js?ver=1.0.0:1550 Restored colors from localStorage: Object
frontend.js?ver=1.0.0:1564 Adding background color to CSS: #000000
frontend.js?ver=1.0.0:1568 Adding text color to CSS: #000000
frontend.js?ver=1.0.0:1573 Body CSS: body.map-theme-custom { background-color: #000000 !important; color: #000000 !important; }
frontend.js?ver=1.0.0:1626 === FINAL CSS ===
frontend.js?ver=1.0.0:1627 Generated CSS: <style id="map-custom-theme-styles">body.map-theme-custom { background-color: #000000 !important; color: #000000 !important; }
                    body.map-theme-custom p,
                    body.map-theme-custom div:not(.map-accessibility-widget):not(.map-accessibility-widget *),
                    body.map-theme-custom span,
                    body.map-theme-custom li,
                    body.map-theme-custom td,
                    body.map-theme-custom th {
                        color: #000000 !important;
                    }
                    body.map-theme-custom h1,
                    body.map-theme-custom h2,
                    body.map-theme-custom h3,
                    body.map-theme-custom h4,
                    body.map-theme-custom h5,
                    body.map-theme-custom h6 {
                        color: #000000 !important;
                    }
                    body.map-theme-custom a {
                        color: #000000 !important;
                    }
                    body.map-theme-custom main,
                    body.map-theme-custom article,
                    body.map-theme-custom section,
                    body.map-theme-custom .content,
                    body.map-theme-custom .post,
                    body.map-theme-custom .page {
                        background-color: #000000 !important;
                    }</style>
frontend.js?ver=1.0.0:1635 === CSS APPLIED ===
frontend.js?ver=1.0.0:1636 Custom theme applied with colors: Object
frontend.js?ver=1.0.0:1446 Custom theme applied directly using stored values: Object
frontend.js?ver=1.0.0:1337 Contrast theme state restored: custom
wordpress/:1 The resource http://localhost/wordpress/wp-content/plugins/plugin/assets/js/frontend.js was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.Understand this warning
wordpress/:1 The resource http://localhost/wordpress/wp-content/plugins/plugin/assets/js/frontend.js was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.