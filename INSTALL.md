# Installation Guide - My Accessibility Plugin

## Quick Installation for XAMPP Testing

### Prerequisites
- XAMPP with PHP 8.0+ and MySQL running
- WordPress 5.0+ installed and configured
- Modern web browser (Chrome, Firefox, Safari, or Edge)

### Step 1: Download and Extract
1. Download the plugin files
2. Extract to your XAMPP WordPress plugins directory:
   ```
   C:\xampp\htdocs\your-wordpress-site\wp-content\plugins\my-accessibility-plugin\
   ```

### Step 2: File Structure Verification
Ensure your plugin directory contains:
```
my-accessibility-plugin/
├── my-accessibility-plugin.php
├── readme.txt
├── INSTALL.md
├── assets/
│   ├── css/
│   │   ├── admin.css
│   │   └── frontend.css
│   └── js/
│       ├── admin.js
│       └── frontend.js
├── includes/
│   ├── class-map-core.php
│   ├── class-map-settings.php
│   ├── class-map-text-to-speech.php
│   ├── class-map-security.php
│   └── class-map-performance.php
├── admin/
│   ├── class-map-admin.php
│   └── class-map-admin-settings.php
├── public/
│   ├── class-map-public.php
│   └── class-map-frontend-widget.php
├── documentation/
│   └── README.md
└── languages/
```

### Step 3: WordPress Installation
1. Open your WordPress admin dashboard (usually `http://localhost/your-site/wp-admin`)
2. Navigate to **Plugins > Installed Plugins**
3. Find "My Accessibility Plugin" in the list
4. Click **Activate**

### Step 4: Initial Configuration
1. After activation, go to **Settings > Accessibility**
2. Configure basic settings:
   - ✅ Enable Text-to-Speech
   - Set Widget Position: "Bottom Right" (recommended)
   - Choose Widget Style: "Modern" (recommended)
   - Set Speech Rate: 1.0 (normal speed)

3. Customize appearance:
   - Button Text: "Listen" (or your preference)
   - Button Color: Choose your brand color
   - Button Size: "Medium" (recommended)

4. Click **Save Changes**

### Step 5: Testing
1. Visit your website frontend
2. You should see the accessibility widget in the chosen position
3. Click the accessibility button to open the panel
4. Test the text-to-speech functionality:
   - Click "Start Reading" to begin speech synthesis
   - Use the speed slider to adjust reading speed
   - Test pause/resume and stop functions

### Step 6: Browser Testing
Test the plugin in different browsers:
- ✅ Chrome (recommended for best speech synthesis support)
- ✅ Firefox
- ✅ Safari
- ✅ Edge

## Troubleshooting

### Plugin Not Appearing
- Check file permissions (should be readable by web server)
- Verify all files are in correct locations
- Check WordPress error logs in `wp-content/debug.log`

### Text-to-Speech Not Working
- Ensure you're using a modern browser with Web Speech API support
- Check browser console for JavaScript errors (F12 > Console)
- Verify microphone permissions aren't blocking speech synthesis
- Try a different browser

### Widget Not Visible
- Check if text-to-speech is enabled in settings
- Look for CSS conflicts with your theme
- Try different widget positions
- Check browser developer tools for styling issues

### Performance Issues
- The plugin is optimized for performance
- If experiencing slowdowns, check for plugin conflicts
- Ensure you're using the latest WordPress version
- Consider enabling WordPress caching

## Development Setup

### For Developers
If you want to modify or extend the plugin:

1. Enable WordPress debug mode in `wp-config.php`:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   define('WP_DEBUG_DISPLAY', false);
   ```

2. Use browser developer tools for frontend debugging
3. Check WordPress debug logs for backend issues
4. Follow WordPress coding standards for any modifications

### File Permissions
Ensure proper file permissions:
- Directories: 755
- PHP files: 644
- Configuration files: 644

### Security Considerations
- The plugin includes built-in security measures
- Rate limiting is enabled for AJAX requests
- All inputs are sanitized and validated
- Nonce verification is implemented for all forms

## Support

### Getting Help
1. Check the documentation in the `documentation/` folder
2. Review the troubleshooting section above
3. Check WordPress error logs
4. Contact support with detailed information:
   - WordPress version
   - PHP version
   - Browser and version
   - Error messages (if any)
   - Steps to reproduce the issue

### Reporting Issues
When reporting issues, please include:
- Detailed description of the problem
- Steps to reproduce
- Expected vs actual behavior
- Browser console errors (if any)
- WordPress debug log entries (if any)

## Next Steps

After successful installation:
1. Explore all settings in the admin panel
2. Test keyboard shortcuts (Alt+A, Alt+S, Alt+X, Esc)
3. Try different widget positions and styles
4. Test on mobile devices
5. Consider accessibility compliance for your specific use case

## Updates

The plugin includes automatic update checking. Future versions will add:
- High contrast mode
- Font size controls
- Additional language support
- Voice selection options
- Advanced customization options

---

**Need Help?** Contact our support team or check the comprehensive documentation in the `documentation/` folder.

**Version**: 1.0.0  
**Tested with**: WordPress 6.4, PHP 8.0+, XAMPP 3.3.0+
