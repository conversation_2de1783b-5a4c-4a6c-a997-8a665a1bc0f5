/**
 * Frontend styles for My Accessibility Plugin
 *
 * @package MyAccessibilityPlugin
 * @since 1.0.0
 */

/* Import OpenDyslexic Font from CDN */
@import url('https://cdn.jsdelivr.net/npm/open-dyslexic@1.0.0/open-dyslexic.min.css');

/* ===== PREMIUM FOCUS SYSTEM - KEYBOARD NAVIGATION ONLY ===== */
*:focus {
    outline: none;
}

*:focus-visible {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3) !important;
    border-color: var(--map-primary) !important;
    transition: all var(--map-transition-base);
}

/* Modern Accessibility Plugin - Unique Design System */
:root {
    /* Primary Brand Colors - Warm & Accessible */
    --map-primary: #6366f1;
    --map-primary-light: #818cf8;
    --map-primary-dark: #4f46e5;
    --map-accent: #f59e0b;
    --map-accent-light: #fbbf24;

    /* Neutral Palette */
    --map-white: #ffffff;
    --map-gray-50: #f9fafb;
    --map-gray-100: #f3f4f6;
    --map-gray-200: #e5e7eb;
    --map-gray-300: #d1d5db;
    --map-gray-400: #9ca3af;
    --map-gray-500: #6b7280;
    --map-gray-600: #4b5563;
    --map-gray-700: #374151;
    --map-gray-800: #1f2937;
    --map-gray-900: #111827;

    /* Semantic Colors */
    --map-success: #059669;
    --map-warning: #d97706;
    --map-error: #dc2626;
    --map-info: #0284c7;

    /* Theme Semantic Variables */
    --map-surface: var(--map-white);
    --map-border: var(--map-gray-300);
    --map-text: var(--map-gray-700);
    --map-text-secondary: var(--map-gray-500);
    --map-bg-rgb: 255, 255, 255;

    /* Design Tokens */
    --map-radius-sm: 6px;
    --map-radius-md: 12px;
    --map-radius-lg: 16px;
    --map-radius-xl: 24px;
    --map-radius-full: 9999px;

    /* Shadows */
    --map-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --map-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --map-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --map-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --map-transition-fast: 150ms ease-out;
    --map-transition-base: 250ms ease-out;
    --map-transition-slow: 350ms ease-out;

    /* Typography */
    --map-font-size-xs: 0.75rem;
    --map-font-size-sm: 0.875rem;
    --map-font-size-base: 1rem;
    --map-font-size-lg: 1.125rem;
    --map-font-size-xl: 1.25rem;

    /* Spacing */
    --map-space-1: 0.25rem;
    --map-space-2: 0.5rem;
    --map-space-3: 0.75rem;
    --map-space-4: 1rem;
    --map-space-5: 1.25rem;
    --map-space-6: 1.5rem;
    --map-space-8: 2rem;
    --map-space-10: 2.5rem;
    --map-space-12: 3rem;

    /* Z-Index Scale */
    --map-z-dropdown: 1000;
    --map-z-sticky: 1020;
    --map-z-fixed: 1030;
    --map-z-modal: 1040;
    --map-z-popover: 1050;
    --map-z-tooltip: 1060;
    --map-z-toast: 1070;
}

/* ===== THEME VARIANTS ===== */

/* Ocean Theme */
.map-theme-ocean {
    --map-primary: #0ea5e9;
    --map-primary-light: #38bdf8;
    --map-primary-dark: #0284c7;
    --map-accent: #06b6d4;
    --map-accent-light: #22d3ee;
}

/* Forest Theme */
.map-theme-forest {
    --map-primary: #059669;
    --map-primary-light: #10b981;
    --map-primary-dark: #047857;
    --map-accent: #65a30d;
    --map-accent-light: #84cc16;
}

/* Sunset Theme */
.map-theme-sunset {
    --map-primary: #ea580c;
    --map-primary-light: #fb923c;
    --map-primary-dark: #c2410c;
    --map-accent: #dc2626;
    --map-accent-light: #ef4444;
}

/* Purple Theme */
.map-theme-purple {
    --map-primary: #7c3aed;
    --map-primary-light: #8b5cf6;
    --map-primary-dark: #6d28d9;
    --map-accent: #a855f7;
    --map-accent-light: #c084fc;
}



/* ===== ACCESSIBILITY WIDGET - MODERN MINIMAL DESIGN ===== */

.map-accessibility-widget {
    position: fixed;
    z-index: var(--map-z-popover);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
    font-size: var(--map-font-size-sm);
    line-height: 1.6;
    color: var(--map-gray-700);
    --webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Widget Positioning */
.map-position-top-left {
    top: 20px;
    left: 20px;
}

.map-position-top-right {
    top: 20px;
    right: 20px;
}

.map-position-bottom-left {
    bottom: 20px;
    left: 20px;
}

.map-position-bottom-right {
    bottom: 20px;
    right: 20px;
}

.map-position-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Main Toggle Button */
/* Main Toggle Button - Unique Floating Design */
.map-main-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    color: var(--map-white);
    border: 2px solid var(--map-white);
    border-radius: var(--map-radius-full);
    width: 56px;
    height: 56px;
    min-width: 56px;
    max-width: 56px;
    min-height: 56px;
    max-height: 56px;
    padding: 0;
    cursor: pointer;
    font-size: var(--map-font-size-sm);
    font-weight: 500;
    box-shadow: var(--map-shadow-lg);
    transition: all var(--map-transition-base);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.map-main-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: inherit;
    transition: opacity var(--map-transition-fast);
    opacity: 0;
}

.map-main-toggle:hover::before {
    opacity: 1;
}

.map-main-toggle:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--map-shadow-xl);
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

.map-main-toggle:active {
    transform: translateY(0) scale(1.02);
    box-shadow: var(--map-shadow-md);
}

.map-main-toggle:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.map-main-toggle[aria-expanded="true"] {
    background: linear-gradient(135deg, var(--map-accent) 0%, var(--map-accent-light) 100%);
    border-color: var(--map-accent);
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.2), var(--map-shadow-lg);
}

/* Ensure button stays circular in all states */
.map-main-toggle,
.map-main-toggle:hover,
.map-main-toggle:active,
.map-main-toggle:focus,
.map-main-toggle[aria-expanded="true"] {
    border-radius: 50% !important;
    box-sizing: border-box !important;
}

/* Toggle Icon */
.map-toggle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-toggle-icon svg {
    width: 32px;
    height: 32px;
    transition: var(--map-transition);
}

/* Button Sizes */
.map-size-small .map-main-toggle {
    width: 50px !important;
    height: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
    min-height: 50px !important;
    max-height: 50px !important;
    font-size: 12px;
}

.map-size-small .map-toggle-icon svg {
    width: 26px;
    height: 26px;
}

.map-size-large .map-main-toggle {
    width: 70px !important;
    height: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
    min-height: 70px !important;
    max-height: 70px !important;
    font-size: 16px;
}

.map-size-large .map-toggle-icon svg {
    width: 38px;
    height: 38px;
}

/* Widget Panel - Modern Card Design with Maximum Bottom Height */
.map-widget-panel {
    position: absolute;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-xl);
    box-shadow: var(--map-shadow-xl);
    width: 480px; /* Increased from 420px */
    height: 700px; /* Significantly increased for maximum bottom space */
    max-height: calc(100vh - 60px); /* Reduced margin for maximum space */
    overflow: hidden;
    color: var(--map-gray-700);
    z-index: var(--map-z-popover);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    animation: slideIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Panel positioning for top positions */
.map-position-top-right .map-widget-panel,
.map-position-top-left .map-widget-panel {
    top: 100%;
    margin-top: 10px; /* Reduced from 20px to move menu up more */
}

.map-position-top-right .map-widget-panel {
    right: -10px; /* Move panel slightly left to show all corners */
}

.map-position-top-left .map-widget-panel {
    left: 0;
}

/* Panel positioning for bottom positions */
.map-position-bottom-right .map-widget-panel,
.map-position-bottom-left .map-widget-panel {
    bottom: -5px; /* Move panel up more (reduced from -15px) */
    margin-bottom: 0;
    margin-top: 0;
}

.map-position-bottom-right .map-widget-panel {
    right: -10px; /* Move panel slightly left to show all corners */
}

.map-position-bottom-left .map-widget-panel {
    left: 0;
}

.map-position-center .map-widget-panel {
    left: 50%;
    transform: translateX(-50%);
}

/* Panel Header - Clean Modern Design */
.map-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--map-space-6) var(--map-space-6) var(--map-space-4);
    border-bottom: 1px solid var(--map-gray-100);
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    position: sticky;
    top: 0;
    z-index: 100;
    flex-shrink: 0;
    min-height: 80px;
    box-sizing: border-box;
}

.map-panel-title {
    margin: 0;
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-800);
    letter-spacing: -0.025em;
}

/* Premium Header Back Button - Unified Circular Icon Style */
.map-header-back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: 50%;
    color: var(--map-primary);
    cursor: pointer;
    transition: all var(--map-transition-base);
    margin: 0;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
}

.map-header-back-button:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--map-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.map-header-back-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

.map-header-back-button svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

/* Hide text span in circular button */
.map-header-back-button span {
    display: none;
}

.map-header-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Premium Reset Button - Unified Circular Icon Style */
.map-reset-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: 50%;
    color: var(--map-primary);
    cursor: pointer;
    transition: all var(--map-transition-base);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
}

.map-reset-button:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--map-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.map-reset-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

.map-reset-button svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    transition: transform var(--map-transition-base);
}

.map-reset-button:hover svg {
    transform: rotate(180deg);
}

/* Hide text span in circular button */
.map-reset-button span {
    display: none;
}

/* Premium Close Button - Unified Circular Icon Style */
.map-close-button {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(99, 102, 241, 0.2);
    cursor: pointer;
    border-radius: 50%;
    color: var(--map-primary);
    transition: all var(--map-transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-size: 18px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
}

.map-close-button:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--map-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.map-close-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

/* Reset confirmation message */
.map-reset-confirmation {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: #059669;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.map-reset-confirmation-show {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

/* Panel Content - Enhanced for Premium Animations */
.map-panel-content {
    padding: var(--map-space-6);
    flex: 1; /* Take remaining space after header */
    overflow: hidden; /* Changed from overflow-y: auto to support animations */
    scrollbar-width: thin;
    scrollbar-color: var(--map-gray-300) transparent;
    scroll-behavior: smooth;
    position: relative;
    box-sizing: border-box;
    min-height: 0; /* Allow flex item to shrink */
}

/* Modal Views Container within Panel Content - Enhanced for Scrolling */
.map-modal-views-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--map-gray-300) transparent;
    /* Ensure smooth scrolling */
    scroll-behavior: smooth;
}

/* Main Toggle Button Fade Behavior */
.map-main-toggle {
    transition: opacity 0.4s ease-out, visibility 0.4s ease-out;
    position: relative;
    z-index: calc(var(--map-z-popover) + 1); /* Above the panel */
}

/* Button fading states */
.map-main-toggle.map-fading-out {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.map-main-toggle.map-panel-open {
    /* Keep button visible briefly when panel opens */
    opacity: 1;
    visibility: visible;
}

/* Ensure button stays above panel during fade */
.map-accessibility-widget.map-panel-active .map-main-toggle {
    position: relative;
    z-index: calc(var(--map-z-popover) + 1);
}

.map-panel-content::-webkit-scrollbar,
.map-modal-views-container::-webkit-scrollbar {
    width: 6px;
}

.map-panel-content::-webkit-scrollbar-track,
.map-modal-views-container::-webkit-scrollbar-track {
    background: transparent;
}

.map-panel-content::-webkit-scrollbar-thumb,
.map-modal-views-container::-webkit-scrollbar-thumb {
    background: var(--map-gray-300);
    border-radius: var(--map-radius-full);
}

.map-panel-content::-webkit-scrollbar-thumb:hover,
.map-modal-views-container::-webkit-scrollbar-thumb:hover {
    background: var(--map-gray-400);
}

/* Feature Sections - Tighter Spacing */
.map-feature-section {
    margin-bottom: var(--map-space-4);
}

.map-feature-section:last-child {
    margin-bottom: 0;
}

/* Add extra spacing after Line Spacing section */
#map-line-spacing-toggle {
    /* Line spacing toggle specific styles if needed */
}

.map-feature-section:has(#map-line-spacing-toggle),
.map-feature-section:nth-last-child(1) {
    margin-bottom: calc(var(--map-space-6) + var(--map-space-4));
}

.map-feature-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--map-text-color);
}

/* Feature Toggle Cards - Modern Design */
.map-feature-toggle {
    display: flex;
    align-items: center;
    width: 100%;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-5);
    cursor: pointer;
    transition: all var(--map-transition-base);
    color: var(--map-gray-700);
    text-align: left;
    margin-bottom: var(--map-space-3);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.map-feature-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
    pointer-events: none;
}

/* Premium Feature Toggle Hover - Matched with Category Dramatic Style */
.map-feature-toggle:hover {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    border-color: var(--map-primary);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Premium Feature Icon Hover Effects - Matched with Category Dramatic Style */
.map-feature-toggle:hover .map-feature-icon {
    transform: scale(1.08) translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

/* Premium Active state for feature toggle - Light Grey with Blue Accents */
.map-feature-toggle.active,
.map-feature-toggle[data-active="true"] {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-gray-100) 100%);
    border: 2px solid var(--map-primary);
    color: var(--map-gray-800);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--map-shadow-lg);
    transform: translateY(-2px);
}

.map-feature-toggle.active::before,
.map-feature-toggle[data-active="true"]::before {
    opacity: 0.1;
}

.map-feature-toggle.active .map-feature-title,
.map-feature-toggle[data-active="true"] .map-feature-title {
    color: var(--map-gray-900);
    font-weight: 600;
}

.map-feature-toggle.active .map-feature-desc,
.map-feature-toggle[data-active="true"] .map-feature-desc {
    color: var(--map-gray-600);
}

/* Premium Feature Icons - Unified with Category Style */
.map-feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-lg);
    margin-right: var(--map-space-4);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
    position: relative;
    z-index: 1;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

.map-feature-icon svg {
    width: 24px;
    height: 24px;
    color: var(--map-white);
    stroke: currentColor;
    fill: currentColor;
    transition: color var(--map-transition-base);
}

.map-feature-toggle.active .map-feature-icon,
.map-feature-toggle[data-active="true"] .map-feature-icon {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.map-feature-toggle.active .map-feature-icon svg,
.map-feature-toggle[data-active="true"] .map-feature-icon svg {
    color: var(--map-white);
}

.map-feature-content {
    flex: 1;
    position: relative;
    z-index: 1;
    pointer-events: none;
}

.map-feature-toggle .map-feature-title {
    margin: 0 0 var(--map-space-1) 0;
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.4;
    transition: color var(--map-transition-base);
}

.map-feature-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    margin: 0;
    line-height: 1.5;
    transition: color var(--map-transition-base);
}

/* Premium Feature Text Hover Effects */
.map-feature-toggle:hover .map-feature-title {
    color: var(--map-gray-900);
}

.map-feature-toggle:hover .map-feature-desc {
    color: var(--map-gray-600);
}

/* Premium Active Feature Toggle Hover Effects */
.map-feature-toggle.active:hover,
.map-feature-toggle[data-active="true"]:hover {
    background: linear-gradient(135deg, var(--map-gray-100) 0%, var(--map-white) 100%);
    border-color: var(--map-primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(99, 102, 241, 0.2), 0 5px 8px rgba(0, 0, 0, 0.08);
}

.map-feature-toggle.active:hover .map-feature-icon,
.map-feature-toggle[data-active="true"]:hover .map-feature-icon {
    transform: scale(1.08) translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
}

/* Premium Toggle Switch - Enhanced Design */
.map-toggle-switch {
    position: relative;
    width: 48px;
    height: 28px;
    background: linear-gradient(135deg, var(--map-gray-200) 0%, var(--map-gray-300) 100%);
    border-radius: var(--map-radius-full);
    border: 1px solid var(--map-gray-300);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.map-toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-50) 100%);
    border: 1px solid var(--map-gray-200);
    border-radius: 50%;
    transition: all var(--map-transition-base);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

/* Premium Blue Toggle Switch for Active State */
.map-feature-toggle[data-active="true"] .map-toggle-switch {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-color: var(--map-primary);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.map-feature-toggle[data-active="true"] .map-toggle-slider {
    transform: translateX(20px);
    background: var(--map-white);
    border-color: var(--map-white);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Premium Toggle Switch Hover Effects - Enhanced Dramatic Style */
.map-feature-toggle:hover .map-toggle-switch {
    background: linear-gradient(135deg, var(--map-gray-300) 0%, var(--map-gray-400) 100%);
    border-color: var(--map-primary);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(99, 102, 241, 0.25);
    transform: scale(1.02);
}

.map-feature-toggle:hover .map-toggle-slider {
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-50) 100%);
    border-color: var(--map-primary);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

/* Premium Active Toggle Switch Hover Effects */
.map-feature-toggle.active:hover .map-toggle-switch,
.map-feature-toggle[data-active="true"]:hover .map-toggle-switch {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    border-color: var(--map-primary-dark);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
    transform: scale(1.02);
}

.map-feature-toggle.active:hover .map-toggle-slider,
.map-feature-toggle[data-active="true"]:hover .map-toggle-slider {
    background: var(--map-white);
    border-color: var(--map-white);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25), 0 2px 5px rgba(0, 0, 0, 0.15);
    transform: translateX(20px) scale(1.05);
}

/* Hide Status Messages to clean up interface */
.map-status-message {
    display: none !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.map-status-icon {
    margin-right: var(--map-space-3);
    font-size: var(--map-font-size-lg);
    flex-shrink: 0;
    margin-top: 2px;
}

.map-status-text {
    font-size: var(--map-font-size-sm);
    line-height: 1.6;
    color: var(--map-gray-600);
}

/* Feature Status Display */
.map-feature-status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    background: rgba(var(--map-primary-rgb), 0.1);
    border: 1px solid rgba(var(--map-primary-rgb), 0.2);
    border-radius: var(--map-radius-md);
    font-size: var(--map-font-size-xs);
    font-weight: 500;
    color: var(--map-primary);
    min-width: 60px;
    position: relative;
    z-index: 1;
    pointer-events: none;
}

.map-feature-toggle.active .map-feature-status,
.map-feature-toggle[data-active="true"] .map-feature-status {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-color: var(--map-primary);
    color: var(--map-white);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

/* Compact Feature Controls Container */
.map-feature-controls {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.9) 100%);
    border-top: 1px solid var(--map-gray-200);
    border-radius: 0 0 var(--map-radius-lg) var(--map-radius-lg);
    padding: var(--map-space-4);
    margin-top: 0;
    transition: all var(--map-transition-base);
    backdrop-filter: blur(10px);
}

/* Compact Control Header */
.map-control-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--map-space-3);
    padding: var(--map-space-2) 0;
}

.map-control-title {
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-700);
    margin: 0;
    letter-spacing: -0.025em;
}

/* Hide Default indicators to save space and clean up interface */
.map-control-value {
    display: none;
}

/* TTS Controls */
.map-tts-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

/* Floating Play Button */
.map-floating-play-button {
    position: absolute;
    z-index: var(--map-z-index);
    animation: mapFadeInUp 0.3s ease-out;
}

.map-play-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--map-primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--map-shadow);
    transition: var(--map-transition);
    position: relative;
}

.map-play-btn:hover {
    background: var(--map-primary-hover);
    transform: scale(1.1);
    box-shadow: var(--map-shadow-hover);
}

.map-play-btn:active {
    background: var(--map-primary-active);
    transform: scale(0.95);
}

.map-play-btn svg {
    margin-left: 2px; /* Slight offset to center the play icon visually */
}

/* Stop button state */
.map-play-btn.map-stop-btn {
    background: #dc2626; /* Red background for stop */
}

.map-play-btn.map-stop-btn:hover {
    background: #b91c1c; /* Darker red on hover */
}

.map-play-btn.map-stop-btn:active {
    background: #991b1b; /* Even darker red when active */
}

.map-play-btn.map-stop-btn svg {
    margin-left: 0; /* No offset needed for square stop icon */
}

/* Loading state */
.map-play-btn.map-loading {
    background: #6b7280; /* Gray background during loading */
    cursor: wait;
}

.map-play-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.map-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: mapSpin 1s ease-in-out infinite;
}

/* Loading spinner animation */
@keyframes mapSpin {
    to {
        transform: rotate(360deg);
    }
}

/* Floating button animation */
@keyframes mapFadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.map-feature-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background: var(--map-primary-color);
    color: var(--map-text-color);
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: var(--map-transition);
    flex: 1;
    justify-content: center;
}

.map-feature-button:hover:not(:disabled) {
    background: var(--map-primary-hover);
}

.map-feature-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.map-feature-button[aria-pressed="true"] {
    background: var(--map-primary-active);
}

/* Progress Bar */
.map-progress-container {
    margin-bottom: 16px;
}

.map-progress-bar {
    width: 100%;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.map-progress-fill {
    height: 100%;
    background: var(--map-primary-color);
    transition: width 0.3s ease;
}

.map-progress-text {
    font-size: 12px;
    color: #666;
}

/* Control Groups */
.map-control-group {
    margin-bottom: 16px;
}

.map-control-label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #23282d;
}

/* Speed Control */
.map-speed-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.map-range-input {
    flex: 1;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.map-range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--map-primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.map-range-input::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: var(--map-primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.map-speed-value {
    font-size: 12px;
    font-weight: 600;
    color: var(--map-primary-color);
    min-width: 30px;
    text-align: center;
}

/* Select Input */
.map-select-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--map-border-color);
    border-radius: 4px;
    background: var(--map-bg-color);
    font-size: 13px;
}

/* Option Groups */
.map-option-group {
    margin-bottom: 12px;
}

.map-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 13px;
}

.map-checkbox-input {
    margin: 0;
}



/* ===== PREMIUM FOCUS STYLES - KEYBOARD ONLY ===== */
.map-focused,
.map-accessibility-widget *:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
    border-radius: var(--map-radius-sm);
    scroll-margin: 20px; /* Prevent focus scroll from causing layout issues */
}

.map-accessibility-widget button:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
    scroll-margin: 20px; /* Prevent focus scroll from causing layout issues */
}

/* Prevent focus-induced scroll issues in panel content */
.map-panel-content *:focus {
    scroll-margin-top: 20px;
    scroll-margin-bottom: 20px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --map-gray-200: #000000;
        --map-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    }

    .map-widget-panel {
        border-width: 2px;
        border-color: #000000;
    }

    .map-feature-toggle {
        border-width: 2px;
    }
}

/* Text Highlighting */
.map-highlight-text {
    background-color: #ffff99 !important;
    color: #000 !important;
    transition: background-color 0.3s ease;
}

/* Responsive Design */
@media (max-width: 480px) {
    .map-widget-panel {
        width: 380px; /* Increased from 340px */
        height: 600px; /* Significantly increased for maximum bottom space */
    }

    .map-panel-content {
        padding: 16px;
    }
    
    .map-position-center {
        position: fixed;
        top: 15px; /* Reduced from 20px to move menu up more */
        left: 10px;
        right: 10px;
        transform: none;
    }
    
    .map-position-center .map-widget-panel {
        width: 100%;
        left: 0;
        transform: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --map-border-color: #000;
        --map-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }
    
    .map-widget-panel {
        border-width: 2px;
    }
}

/* Reduced Motion - Accessibility Support */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }

    /* Simplified transitions for reduced motion */
    .map-modal-view {
        transition: opacity 0.2s ease !important;
        transform: none !important;
    }

    .map-modal-view.map-view-entering-forward,
    .map-modal-view.map-view-entering-backward {
        animation: fadeIn 0.2s ease forwards !important;
    }

    .map-modal-view.map-view-exiting-forward,
    .map-modal-view.map-view-exiting-backward {
        animation: fadeOut 0.2s ease forwards !important;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
}

/* ===== CONTRAST THEMES SECTION - UNIFIED WITH TEXT CATEGORY ===== */

/* Visual Themes and Color Studio now use standard map-feature-toggle styling */
/* All styling handled by existing .map-feature-toggle, .map-feature-controls classes */

/* ===== COMPACT THEME SELECTOR - MATCHING TEXT CATEGORY STYLE ===== */

/* Theme Selector Container - Compact Design */
.map-theme-selector {
    padding: var(--map-space-4);
    background: var(--map-white);
    border-radius: var(--map-radius-md);
    pointer-events: auto;
    position: relative;
    border: 1px solid var(--map-gray-200);
}

/* Theme Counter - Removed for cleaner design */

/* Theme Preview Card - Compact Style */
.map-theme-preview-card {
    position: relative;
    background: var(--map-white);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    margin-bottom: var(--map-space-3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--map-gray-200);
    transition: all var(--map-transition-base);
}

.map-theme-preview-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Navigation Arrows */
/* Navigation Arrows - Compact Style */
.map-theme-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 36px;
    height: 36px;
    border: 1px solid var(--map-gray-300);
    border-radius: 50%;
    background: var(--map-white);
    color: var(--map-gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all var(--map-transition-base);
    z-index: 2;
}

.map-theme-nav:hover {
    background: var(--map-primary);
    color: var(--map-white);
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
}

.map-theme-nav:active {
    transform: translateY(-50%) scale(0.95);
}

.map-theme-nav-prev {
    left: -18px;
}

.map-theme-nav-next {
    right: -18px;
}



/* Theme Icon Preview */
.map-theme-icon-preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--map-space-4);
}

.map-theme-icon-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--map-primary) 0%, #5b21b6 100%);
    color: var(--map-white);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 10px rgba(99, 102, 241, 0.3);
    position: relative;
    overflow: hidden;
}

.map-theme-icon-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.map-theme-icon-preview:hover::before {
    opacity: 1;
}

.map-theme-icon-preview svg {
    width: 28px;
    height: 28px;
    transition: transform 0.3s ease;
}

.map-theme-icon-preview:hover svg {
    transform: scale(1.1);
}

/* Theme-specific icon preview colors */
.map-theme-icon-preview[data-theme="normal"] {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.map-theme-icon-preview[data-theme="monochrome"] {
    background: linear-gradient(135deg, #6b7280 0%, #374151 100%);
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.map-theme-icon-preview[data-theme="low-saturation"] {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.map-theme-icon-preview[data-theme="high-saturation"] {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.map-theme-icon-preview[data-theme="dark"] {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    box-shadow: 0 4px 15px rgba(31, 41, 55, 0.4);
}

.map-theme-icon-preview[data-theme="high-contrast"] {
    background: linear-gradient(135deg, #000000 0%, #374151 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.map-theme-icon-preview[data-theme="sepia"] {
    background: linear-gradient(135deg, #92400e 0%, #78350f 100%);
    box-shadow: 0 4px 15px rgba(146, 64, 14, 0.3);
}

.map-theme-icon-preview[data-theme="colorblind"] {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}



/* Theme transition animations */
@keyframes themeSlideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes iconPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.map-theme-info {
    animation: themeSlideIn 0.4s ease-out;
}

.map-theme-icon-preview {
    animation: iconPulse 0.6s ease-out;
}

/* Active theme glow effect */
.map-theme-icon-preview.applying {
    animation: applyingGlow 1s ease-in-out;
}

@keyframes applyingGlow {
    0%, 100% {
        box-shadow: 0 3px 10px rgba(99, 102, 241, 0.3);
    }
    50% {
        box-shadow: 0 6px 20px rgba(99, 102, 241, 0.6), 0 0 25px rgba(99, 102, 241, 0.4);
        transform: scale(1.08);
    }
}



/* Theme Info */
/* Theme Info - Perfectly Centered Under Icon */
.map-theme-info {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: var(--map-space-3) 0 var(--map-space-2) 0;
}

.map-theme-title {
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    margin: 0;
    letter-spacing: -0.025em;
    text-align: center;
    width: 100%;
}

/* Theme Dots Indicator - No Extra Space */
.map-theme-dots {
    display: flex;
    justify-content: center;
    gap: var(--map-space-2);
    margin: 0;
}

.map-theme-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--map-gray-300);
    background: var(--map-white);
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.map-theme-dot:hover {
    border-color: var(--map-primary);
    transform: scale(1.2);
}

.map-theme-dot.active {
    background: var(--map-primary);
    border-color: var(--map-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* Default subtle focus for theme dot */
.map-theme-dot:focus {
    outline: 2px solid var(--map-gray-400);
    outline-offset: 1px;
}







/* ===== RESPONSIVE DESIGN FOR THEME SELECTOR ===== */

@media (max-width: 768px) {
    .map-theme-selector {
        padding: var(--map-space-4);
    }

    .map-theme-preview-card {
        padding: var(--map-space-4);
        margin-bottom: var(--map-space-4);
    }



    .map-theme-nav {
        width: 36px;
        height: 36px;
    }

    .map-theme-nav-prev {
        left: -18px;
    }

    .map-theme-nav-next {
        right: -18px;
    }

    .map-theme-title {
        font-size: var(--map-font-size-base);
    }

    .map-theme-desc {
        font-size: var(--map-font-size-xs);
    }

    .map-theme-dot {
        width: 10px;
        height: 10px;
    }



    .map-theme-icon-preview {
        width: 42px;
        height: 42px;
    }

    .map-theme-icon-preview svg {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .map-theme-selector {
        padding: var(--map-space-3);
    }

    .map-theme-preview-card {
        padding: var(--map-space-3);
    }



    .map-theme-nav {
        width: 32px;
        height: 32px;
    }

    .map-theme-nav-prev {
        left: -16px;
    }

    .map-theme-nav-next {
        right: -16px;
    }

    .map-theme-counter {
        font-size: var(--map-font-size-xs);
    }
}

/* Legacy theme button styles removed - using new theme selector design */

.map-theme-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--map-shadow-lg);
    border-color: var(--map-primary-light);
}

.map-theme-button.map-theme-active {
    border-color: var(--map-primary);
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(129, 140, 248, 0.05) 100%);
    box-shadow: var(--map-shadow-md);
}

.map-theme-button.map-theme-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
}

/* Theme Preview */
.map-theme-preview {
    width: 60px;
    height: 40px;
    border-radius: var(--map-radius-md);
    overflow: hidden;
    position: relative;
    box-shadow: var(--map-shadow-sm);
    border: 1px solid var(--map-gray-200);
}

.map-theme-preview-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--map-white);
}

.map-theme-preview-content {
    position: absolute;
    top: 6px;
    left: 6px;
    right: 6px;
    bottom: 6px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.map-theme-preview-text {
    height: 3px;
    background: var(--map-gray-700);
    border-radius: 1px;
    width: 80%;
}

.map-theme-preview-accent {
    height: 2px;
    background: var(--map-primary);
    border-radius: 1px;
    width: 60%;
}

/* Dark Theme Preview */
.map-theme-preview-dark .map-theme-preview-bg {
    background: #1a1a1a;
}

.map-theme-preview-dark .map-theme-preview-text {
    background: #ffffff;
}

.map-theme-preview-dark .map-theme-preview-accent {
    background: #60a5fa;
}

/* High Contrast Theme Preview */
.map-theme-preview-high-contrast .map-theme-preview-bg {
    background: #000000;
}

.map-theme-preview-high-contrast .map-theme-preview-text {
    background: #ffffff;
}

.map-theme-preview-high-contrast .map-theme-preview-accent {
    background: #ffffff;
}

/* Sepia Theme Preview */
.map-theme-preview-sepia .map-theme-preview-bg {
    background: #f4f1e8;
}

.map-theme-preview-sepia .map-theme-preview-text {
    background: #5c4b37;
}

.map-theme-preview-sepia .map-theme-preview-accent {
    background: #8b6914;
}

/* Monochrome Theme Preview */
.map-theme-preview-monochrome .map-theme-preview-bg {
    background: #f8f9fa;
}

.map-theme-preview-monochrome .map-theme-preview-text {
    background: #6c757d; /* Medium gray */
}

.map-theme-preview-monochrome .map-theme-preview-accent {
    background: #495057; /* Dark gray */
}

/* Low Saturation Theme Preview */
.map-theme-preview-low-saturation .map-theme-preview-bg {
    background: #ffffff;
}

.map-theme-preview-low-saturation .map-theme-preview-text {
    background: #6b7280; /* Desaturated gray */
}

.map-theme-preview-low-saturation .map-theme-preview-accent {
    background: #9ca3af; /* Muted accent */
}

/* High Saturation Theme Preview */
.map-theme-preview-high-saturation .map-theme-preview-bg {
    background: #ffffff;
}

.map-theme-preview-high-saturation .map-theme-preview-text {
    background: #1e40af; /* Vibrant blue */
}

.map-theme-preview-high-saturation .map-theme-preview-accent {
    background: #dc2626; /* Vibrant red */
}

/* Color Blind Theme Preview */
.map-theme-preview-colorblind .map-theme-preview-bg {
    background: #fafafa;
}

.map-theme-preview-colorblind .map-theme-preview-text {
    background: #2563eb;
}

.map-theme-preview-colorblind .map-theme-preview-accent {
    background: #dc2626;
}

/* Theme Info */
.map-theme-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: var(--map-space-2);
}

.map-theme-name {
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-700);
    text-align: center;
    flex: 1;
    line-height: 1.3;
}

.map-theme-active .map-theme-name {
    color: var(--map-primary);
}

.map-theme-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--map-gray-200);
    color: var(--map-white);
    opacity: 0;
    transition: all var(--map-transition-base);
    flex-shrink: 0;
}

.map-theme-active .map-theme-check {
    background: var(--map-primary);
    opacity: 1;
    transform: scale(1.1);
}

.map-theme-check svg {
    width: 12px;
    height: 12px;
}

/* Theme Application Classes */
body.map-theme-dark {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

body.map-theme-dark * {
    background-color: inherit !important;
    color: inherit !important;
    border-color: #404040 !important;
}

body.map-theme-dark a {
    color: #60a5fa !important;
}

body.map-theme-high-contrast {
    background-color: #000000 !important;
    color: #ffffff !important;
}

body.map-theme-high-contrast * {
    background-color: #000000 !important;
    color: #ffffff !important;
    border-color: #ffffff !important;
}

body.map-theme-high-contrast a {
    color: #ffffff !important;
    text-decoration: underline !important;
}

body.map-theme-sepia {
    background-color: #f4f1e8 !important;
    color: #5c4b37 !important;
}

body.map-theme-sepia * {
    background-color: inherit !important;
    color: inherit !important;
    border-color: #d4c5a9 !important;
}

body.map-theme-sepia a {
    color: #8b6914 !important;
}

/* Monochrome Theme - Convert everything to grayscale */
body.map-theme-monochrome main,
body.map-theme-monochrome article,
body.map-theme-monochrome section,
body.map-theme-monochrome div:not(.map-accessibility-widget),
body.map-theme-monochrome header,
body.map-theme-monochrome footer,
body.map-theme-monochrome nav,
body.map-theme-monochrome aside,
body.map-theme-monochrome p,
body.map-theme-monochrome h1,
body.map-theme-monochrome h2,
body.map-theme-monochrome h3,
body.map-theme-monochrome h4,
body.map-theme-monochrome h5,
body.map-theme-monochrome h6,
body.map-theme-monochrome img,
body.map-theme-monochrome video,
body.map-theme-monochrome canvas,
body.map-theme-monochrome svg {
    filter: grayscale(1) !important;
}

/* Low Saturation Theme - Target main content areas to avoid positioning issues */
body.map-theme-low-saturation main,
body.map-theme-low-saturation article,
body.map-theme-low-saturation section,
body.map-theme-low-saturation div:not(.map-accessibility-widget),
body.map-theme-low-saturation header,
body.map-theme-low-saturation footer,
body.map-theme-low-saturation nav,
body.map-theme-low-saturation aside,
body.map-theme-low-saturation p,
body.map-theme-low-saturation h1,
body.map-theme-low-saturation h2,
body.map-theme-low-saturation h3,
body.map-theme-low-saturation h4,
body.map-theme-low-saturation h5,
body.map-theme-low-saturation h6,
body.map-theme-low-saturation img,
body.map-theme-low-saturation video,
body.map-theme-low-saturation canvas,
body.map-theme-low-saturation svg {
    filter: saturate(0.88) !important;
}

/* High Saturation Theme - Target main content areas to avoid positioning issues */
body.map-theme-high-saturation main,
body.map-theme-high-saturation article,
body.map-theme-high-saturation section,
body.map-theme-high-saturation div:not(.map-accessibility-widget),
body.map-theme-high-saturation header,
body.map-theme-high-saturation footer,
body.map-theme-high-saturation nav,
body.map-theme-high-saturation aside,
body.map-theme-high-saturation p,
body.map-theme-high-saturation h1,
body.map-theme-high-saturation h2,
body.map-theme-high-saturation h3,
body.map-theme-high-saturation h4,
body.map-theme-high-saturation h5,
body.map-theme-high-saturation h6,
body.map-theme-high-saturation img,
body.map-theme-high-saturation video,
body.map-theme-high-saturation canvas,
body.map-theme-high-saturation svg {
    filter: saturate(1.25) !important;
}

/* Ensure accessibility widget is never affected by any filters */
.map-accessibility-widget,
.map-accessibility-widget *,
.map-accessibility-widget .map-widget-panel,
.map-accessibility-widget .map-main-toggle {
    filter: none !important;
}

/* Ensure monochrome theme doesn't affect the accessibility widget */
body.map-theme-monochrome .map-accessibility-widget,
body.map-theme-monochrome .map-accessibility-widget * {
    filter: none !important;
}

/* ===== COLOR STUDIO - CLEAN DESIGN ===== */

/* Ensure hover effect works properly for Color Studio section */
.map-color-studio {
    position: relative;
}

/* Studio Header - Clean Style */
.map-studio-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--map-space-5);
    background: rgba(var(--map-bg-rgb), 0.3);
    border-radius: var(--map-radius-lg) var(--map-radius-lg) 0 0;
    margin-bottom: 0;
    position: relative;
    z-index: 2;
    border: none;
    cursor: pointer;
    transition: all var(--map-transition-base);
}

.map-custom-theme-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.map-studio-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--map-primary);
    color: var(--map-white);
    border-radius: 6px;
    flex-shrink: 0;
    transition: all var(--map-transition-base);
}

.map-custom-theme-text {
    flex: 1;
}

.map-custom-theme-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--map-gray-800);
    margin-bottom: 2px;
    line-height: 1.4;
}

.map-custom-theme-desc {
    font-size: 14px;
    color: var(--map-gray-500);
    line-height: 1.3;
    margin: 0;
}

.map-studio-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: var(--map-gray-400);
    transition: all var(--map-transition-base);
    margin-left: var(--map-space-3);
}

.map-studio-header[aria-expanded="true"] .map-studio-arrow {
    transform: rotate(90deg);
}

/* Studio Content */
.map-studio-content {
    padding: var(--map-space-6);
    background: var(--map-white);
    border-radius: 0 0 var(--map-radius-lg) var(--map-radius-lg);
    position: relative;
    z-index: 2;
}

/* ===== PREMIUM COMPACT COLOR STUDIO LAYOUT ===== */

/* Main Container - Matching Feature Controls Pattern */
.map-color-studio-compact {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
    margin-bottom: var(--map-space-4);
}

/* Color Row - Perfect Alignment for Icons and Text */
.map-color-row {
    display: flex;
    align-items: center; /* Center all content vertically */
    justify-content: space-between;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    transition: all var(--map-transition-base);
    min-height: 64px; /* Slightly taller for better text alignment */
    box-sizing: border-box;
}

.map-color-row:hover {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    border-color: var(--map-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
}

/* Color Label Section - Perfect Icon and Text Alignment */
.map-color-label {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0; /* Prevent flex shrinking issues */
    gap: var(--map-space-3); /* Consistent spacing between icon and text */
}

/* Color Icon - Fixed Width for Consistent Alignment */
.map-color-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: var(--map-radius-md);
    color: var(--map-white);
    transition: all var(--map-transition-base);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
    flex-shrink: 0;
}

.map-color-row:hover .map-color-icon {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

/* Color Info - Perfect Text Alignment */
.map-color-info {
    display: flex;
    flex-direction: column;
    justify-content: center; /* Center text vertically relative to icon */
    gap: 2px;
    flex: 1;
    min-width: 0; /* Prevent text overflow issues */
}

.map-color-title {
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.2;
    margin: 0;
    white-space: nowrap; /* Prevent title wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
}

.map-color-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    line-height: 1.3;
    margin: 0;
    white-space: nowrap; /* Prevent description wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Color Controls Section - Fixed Width to Prevent Layout Shifts */
.map-color-controls {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
    flex-shrink: 0;
    width: 40px; /* Back to original width - only color picker */
    justify-content: flex-end;
    position: relative;
}

/* Color Picker Wrapper - Contains picker and overlay reset button */
.map-color-picker-wrapper {
    position: relative;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

/* Compact Color Picker - Fixed Size to Prevent Layout Shifts */
.map-color-picker-compact {
    width: 100%;
    height: 100%;
    border: 3px solid var(--map-primary);
    border-radius: var(--map-radius-md);
    cursor: pointer;
    transition: all var(--map-transition-base);
    background: transparent;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
    box-sizing: border-box; /* Include border in size calculation */
}

.map-color-picker-compact:hover {
    border-color: var(--map-primary-dark);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.map-color-picker-compact:focus-visible {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
    border-color: var(--map-primary);
}

/* Compact Color Picker Browser Specific Styles - Remove White Space */
.map-color-picker-compact::-webkit-color-swatch-wrapper {
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    border-radius: calc(var(--map-radius-md) - 3px) !important;
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box !important;
}

.map-color-picker-compact::-webkit-color-swatch {
    border: none !important;
    border-radius: calc(var(--map-radius-md) - 3px) !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box !important;
}

.map-color-picker-compact::-moz-color-swatch {
    border: none !important;
    border-radius: calc(var(--map-radius-md) - 3px) !important;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box !important;
}

/* Premium Overlay Reset Button - Inside Color Picker */
.map-color-reset-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 50%;
    color: #ef4444;
    cursor: pointer;
    transition: all var(--map-transition-base);
    padding: 0;
    opacity: 0;
    transform: scale(0);
    pointer-events: none;
    box-sizing: border-box;
    position: absolute;
    top: -2px;
    right: -2px;
    z-index: 10;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.map-color-reset-btn.visible {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
}

.map-color-reset-btn:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: var(--map-white);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.map-color-reset-btn:active {
    transform: scale(0.9);
}

/* Show reset button on color picker hover */
.map-color-picker-wrapper:hover .map-color-reset-btn.visible {
    opacity: 1;
    transform: scale(1);
}

/* Premium Hover Effects for Color Rows - Enhanced for Clickability */
.map-color-row {
    cursor: pointer;
    transition: all var(--map-transition-base);
}

.map-color-row:hover {
    background: rgba(99, 102, 241, 0.02);
    border-radius: var(--map-radius-md);
    transform: translateY(-1px);
}

.map-color-row:hover .map-color-title {
    color: var(--map-gray-900);
}

.map-color-row:hover .map-color-desc {
    color: var(--map-gray-600);
}

/* Mobile Responsive - Compact Layout */
@media (max-width: 480px) {
    .map-color-row {
        flex-direction: column;
        align-items: stretch;
        gap: var(--map-space-3);
        padding: var(--map-space-3);
        min-height: auto;
    }

    .map-color-label {
        justify-content: flex-start;
    }

    .map-color-controls {
        justify-content: center;
        gap: var(--map-space-3);
    }

    .map-color-picker-compact {
        width: 36px;
        height: 36px;
    }

    .map-color-reset-btn {
        width: 28px;
        height: 28px;
    }
}

/* Color Cards */
.map-color-card {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: var(--map-space-5);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
    box-shadow:
        0 1px 3px 0 rgba(0, 0, 0, 0.1),
        0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.map-color-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.map-color-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 10px 25px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: rgba(102, 126, 234, 0.3);
}

.map-color-card:hover::before {
    opacity: 1;
}

/* Color Card Header */
.map-color-card-header {
    display: flex;
    align-items: flex-start;
    gap: var(--map-space-3);
    margin-bottom: var(--map-space-4);
}

.map-color-card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: var(--map-space-3);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.map-color-card:hover .map-color-card-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
}

.map-color-card-info {
    flex: 1;
}

.map-color-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--map-gray-900);
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.map-color-card-desc {
    font-size: 13px;
    color: var(--map-gray-600);
    margin: 0;
    line-height: 1.4;
}

/* Premium Color Reset Button - Blue Gradient Style */
.map-color-reset {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    color: var(--map-white);
    border: 1px solid var(--map-primary);
    border-radius: var(--map-radius-md);
    padding: var(--map-space-2);
    cursor: pointer;
    transition: all var(--map-transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
    position: relative;
    overflow: hidden;
    width: 32px;
    height: 32px;
}

.map-color-reset::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-color-card:hover .map-color-reset {
    opacity: 1;
    transform: scale(1);
}

.map-color-reset:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    color: var(--map-white);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.25);
}

.map-color-reset:hover::before {
    opacity: 1;
}

.map-color-reset:active {
    transform: scale(0.95);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

/* Color Picker Wrapper - Invisible Container (Different context - not used in compact layout) */
.map-studio-color-picker-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--map-space-4);
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.map-studio-color-picker-wrapper:hover {
    background: rgba(248, 250, 252, 1);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Studio Color Picker */
.map-studio-picker {
    width: 60px;
    height: 60px;
    border: 3px solid rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: none;
    padding: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 2px 4px rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.map-studio-picker::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: 18px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.map-studio-picker:hover::before {
    opacity: 1;
}

.map-studio-picker:hover {
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 1);
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.map-studio-picker:focus {
    outline: none;
    transform: scale(1.05);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.3),
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.map-studio-picker:active {
    transform: scale(0.95);
}

/* Color Picker Browser Specific Styles */
.map-studio-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 13px;
}

.map-studio-picker::-webkit-color-swatch {
    border: none;
    border-radius: 13px;
}

.map-studio-picker::-moz-color-swatch {
    border: none;
    border-radius: 13px;
}

/* Color Preview - Clean Professional Design */
.map-color-preview {
    flex: 1;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    position: relative;
    overflow: hidden;
}

/* Add subtle pattern for empty state */
.map-color-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(0,0,0,0.03) 25%,
        transparent 25%,
        transparent 75%,
        rgba(0,0,0,0.03) 75%),
    linear-gradient(45deg,
        rgba(0,0,0,0.03) 25%,
        transparent 25%,
        transparent 75%,
        rgba(0,0,0,0.03) 75%);
    background-size: 8px 8px;
    background-position: 0 0, 4px 4px;
    opacity: 0.5;
}

/* Add subtle inner shadow for depth */
.map-color-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 7px;
}



/* Reset Feedback Animation */
.map-reset-feedback {
    animation: resetPulse 0.6s ease-in-out;
}

@keyframes resetPulse {
    0% { transform: scale(1); }
    50% {
        transform: scale(0.98);
        box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.3);
    }
    100% { transform: scale(1); }
}

/* Enhanced Accessibility */
.map-color-card:focus-within {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.map-studio-picker:focus-visible {
    outline: 3px solid #667eea;
    outline-offset: 3px;
}

.map-color-reset:focus-visible {
    outline: 2px solid #dc2626;
    outline-offset: 2px;
}

/* Loading States */
.map-color-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.map-color-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}



.map-collapsible-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--map-space-4) var(--map-space-5);
    background: var(--map-white);
    border: none;
    cursor: pointer;
    transition: all var(--map-transition-base);
    text-align: left;
    position: relative;
    z-index: 1;
    pointer-events: auto;
}

.map-collapsible-header:hover {
    background: var(--map-gray-50);
}

.map-custom-theme-info {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
}

.map-custom-theme-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
    border-radius: var(--map-radius-lg);
    color: var(--map-white);
    flex-shrink: 0;
    box-shadow: var(--map-shadow-sm);
}

.map-custom-theme-emoji {
    font-size: 18px;
}

.map-custom-theme-text {
    flex: 1;
}

.map-custom-theme-title {
    margin: 0 0 var(--map-space-1) 0;
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.4;
}

.map-custom-theme-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    line-height: 1.3;
    margin: 0;
}

.map-custom-theme-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    color: var(--map-gray-400);
    transition: transform var(--map-transition-base);
}

.map-collapsible-header[aria-expanded="true"] .map-custom-theme-arrow {
    transform: rotate(90deg);
}

/* Custom Theme Content */
.map-custom-theme-content {
    padding: var(--map-space-5);
    background: var(--map-white);
    border-radius: 0 0 var(--map-radius-lg) var(--map-radius-lg);
    pointer-events: auto;
    position: relative;
    z-index: 1;
}

/* Color Controls */
.map-custom-theme-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--map-space-3);
    margin-bottom: var(--map-space-6);
}

.map-color-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--map-space-2);
    padding: var(--map-space-3);
    background: var(--map-gray-50);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-md);
    transition: all var(--map-transition-base);
    pointer-events: auto;
    position: relative;
    text-align: center;
}

.map-color-control:hover {
    border-color: var(--map-primary-light);
    box-shadow: var(--map-shadow-sm);
}

.map-color-label {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
    font-size: var(--map-font-size-sm);
    font-weight: 500;
    color: var(--map-gray-700);
    cursor: pointer;
    justify-content: center;
}

.map-color-icon {
    font-size: 16px;
}

.map-color-picker {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: var(--map-radius-md);
    cursor: pointer;
    transition: all var(--map-transition-base);
    background: none;
    padding: 0;
    pointer-events: auto;
    position: relative;
    z-index: 2;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.map-color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: var(--map-radius-md);
}

.map-color-picker::-webkit-color-swatch {
    border: none;
    border-radius: var(--map-radius-md);
}

.map-color-picker::-moz-color-swatch {
    border: none;
    border-radius: var(--map-radius-md);
}

.map-color-picker:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Default subtle focus for color picker */
.map-color-picker:focus {
    outline: 2px solid var(--map-gray-400);
    outline-offset: 1px;
}









/* Custom Theme Applied State */
body.map-theme-custom {
    /* Custom theme styles will be applied dynamically via JavaScript */
}



/* ===== PREFERENCES FEATURES ===== */

/* Widget Position Grid */
.map-position-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--map-space-3);
    margin-top: var(--map-space-4);
}

.map-position-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--map-space-2);
    padding: var(--map-space-4);
    background: var(--map-white);
    border: 2px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    cursor: pointer;
    transition: all var(--map-transition-base);
    min-height: 80px;
}

.map-position-option:hover {
    border-color: var(--map-primary-light);
    background: var(--map-primary-50);
}

.map-position-option.active {
    border-color: var(--map-primary);
    background: var(--map-primary-50);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.map-position-icon {
    font-size: 24px;
}

.map-position-label {
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-700);
}

.map-position-option.active .map-position-label {
    color: var(--map-primary);
}

/* Action Buttons */
.map-action-button {
    display: flex;
    align-items: center;
    gap: var(--map-space-2);
    padding: var(--map-space-3) var(--map-space-4);
    background: var(--map-primary);
    color: var(--map-white);
    border: none;
    border-radius: var(--map-radius-md);
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--map-transition-base);
    min-height: 44px;
}

.map-action-button:hover {
    background: var(--map-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--map-shadow-md);
}

.map-action-button:active {
    transform: translateY(0);
}

.map-danger-button {
    background: var(--map-error);
}

.map-danger-button:hover {
    background: #dc2626;
}

/* Widget Position Classes */
.map-position-top-left {
    top: 20px !important;
    left: 20px !important;
    right: auto !important;
    bottom: auto !important;
}

.map-position-top-right {
    top: 20px !important;
    right: 20px !important;
    left: auto !important;
    bottom: auto !important;
}

.map-position-bottom-left {
    bottom: 20px !important;
    left: 20px !important;
    right: auto !important;
    top: auto !important;
}

.map-position-bottom-right {
    bottom: 20px !important;
    right: 20px !important;
    left: auto !important;
    top: auto !important;
}

/* Feature Action Container */
.map-feature-action {
    display: flex;
    align-items: center;
}

/* Color Blind Friendly Theme - Uses safe color combinations */
body.map-theme-colorblind {
    background-color: #ffffff !important;
    color: #000000 !important;
}

body.map-theme-colorblind * {
    /* Reset problematic colors to safe alternatives */
    background-color: inherit !important;
    color: inherit !important;
    border-color: #000000 !important;
}

/* Color blind safe link colors - use blue and underlines */
body.map-theme-colorblind a {
    color: #0066cc !important;
    text-decoration: underline !important;
}

body.map-theme-colorblind a:hover {
    color: #004499 !important;
    text-decoration: underline !important;
}

body.map-theme-colorblind a:visited {
    color: #663399 !important;
    text-decoration: underline !important;
}

/* Safe button colors - high contrast blue and yellow */
body.map-theme-colorblind button,
body.map-theme-colorblind .button,
body.map-theme-colorblind input[type="button"],
body.map-theme-colorblind input[type="submit"] {
    background-color: #0066cc !important;
    color: #ffffff !important;
    border: 2px solid #000000 !important;
}

body.map-theme-colorblind button:hover,
body.map-theme-colorblind .button:hover,
body.map-theme-colorblind input[type="button"]:hover,
body.map-theme-colorblind input[type="submit"]:hover {
    background-color: #ffcc00 !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
}

/* Form elements with high contrast */
body.map-theme-colorblind input,
body.map-theme-colorblind textarea,
body.map-theme-colorblind select {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
}

body.map-theme-colorblind input:focus,
body.map-theme-colorblind textarea:focus,
body.map-theme-colorblind select:focus {
    background-color: #ffffcc !important;
    color: #000000 !important;
    border: 3px solid #0066cc !important;
    outline: none !important;
}

/* Safe colors for success/error states */
body.map-theme-colorblind .success,
body.map-theme-colorblind .notice-success {
    background-color: #ffffff !important;
    color: #000000 !important;
    border: 3px solid #000000 !important;
    border-left: 8px solid #000000 !important;
}

body.map-theme-colorblind .error,
body.map-theme-colorblind .notice-error {
    background-color: #000000 !important;
    color: #ffffff !important;
    border: 3px solid #ffffff !important;
}

body.map-theme-colorblind .warning,
body.map-theme-colorblind .notice-warning {
    background-color: #ffcc00 !important;
    color: #000000 !important;
    border: 3px solid #000000 !important;
}

/* Navigation and menu items */
body.map-theme-colorblind nav,
body.map-theme-colorblind .menu,
body.map-theme-colorblind .nav {
    background-color: #f0f0f0 !important;
    border: 2px solid #000000 !important;
}

body.map-theme-colorblind nav a,
body.map-theme-colorblind .menu a,
body.map-theme-colorblind .nav a {
    color: #0066cc !important;
    text-decoration: underline !important;
    background-color: transparent !important;
}

body.map-theme-colorblind nav a:hover,
body.map-theme-colorblind .menu a:hover,
body.map-theme-colorblind .nav a:hover {
    background-color: #ffcc00 !important;
    color: #000000 !important;
}

/* Headers with clear hierarchy */
body.map-theme-colorblind h1,
body.map-theme-colorblind h2,
body.map-theme-colorblind h3,
body.map-theme-colorblind h4,
body.map-theme-colorblind h5,
body.map-theme-colorblind h6 {
    color: #000000 !important;
    background-color: transparent !important;
    border-bottom: 2px solid #000000 !important;
    padding-bottom: 4px !important;
}

/* Tables with clear borders */
body.map-theme-colorblind table {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
}

body.map-theme-colorblind th,
body.map-theme-colorblind td {
    border: 1px solid #000000 !important;
    background-color: #ffffff !important;
    color: #000000 !important;
}

body.map-theme-colorblind th {
    background-color: #f0f0f0 !important;
    font-weight: bold !important;
}

/* Ensure images have alt text visibility */
body.map-theme-colorblind img:not([alt]),
body.map-theme-colorblind img[alt=""] {
    border: 3px dashed #ff0000 !important;
    background-color: #ffcccc !important;
}

/* High contrast for accessibility widget itself */
body.map-theme-colorblind .map-accessibility-widget {
    background-color: #ffffff !important;
    border: 3px solid #000000 !important;
}

body.map-theme-colorblind .map-widget-panel {
    background-color: #ffffff !important;
    border: 3px solid #000000 !important;
    color: #000000 !important;
}

/* Responsive Design for Clean Headers */
@media (max-width: 768px) {
    .map-contrast-themes-header .map-collapsible-header,
    .map-studio-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--map-space-3);
        padding: var(--map-space-4);
    }

    .map-contrast-themes-info,
    .map-custom-theme-info {
        width: 100%;
    }

    .map-contrast-themes-current {
        align-self: flex-end;
        min-width: 60px;
        height: 26px;
        padding: 4px 8px;
        font-size: 12px;
    }

    .map-contrast-themes-icon,
    .map-studio-icon {
        width: 32px;
        height: 32px;
    }

    .map-contrast-themes-title,
    .map-custom-theme-title {
        font-size: 15px;
    }

    .map-contrast-themes-desc,
    .map-custom-theme-desc {
        font-size: 13px;
    }

    .map-contrast-themes-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--map-space-2);
        padding: var(--map-space-4);
    }

    /* Color Studio Responsive */
    .map-studio-grid {
        grid-template-columns: 1fr;
        gap: var(--map-space-4);
    }

    .map-color-card {
        padding: var(--map-space-4);
    }

    .map-color-card-header {
        gap: var(--map-space-2);
        margin-bottom: var(--map-space-3);
    }

    .map-color-card-icon {
        padding: var(--map-space-2);
    }

    .map-color-card-title {
        font-size: 15px;
    }

    .map-color-card-desc {
        font-size: 12px;
    }

    .map-color-picker-wrapper {
        flex-direction: column;
        gap: var(--map-space-3);
        padding: var(--map-space-3);
    }

    .map-studio-picker {
        width: 50px;
        height: 50px;
    }

    .map-studio-content {
        padding: var(--map-space-4);
    }

    .map-custom-theme-title {
        font-size: 16px;
    }

    .map-custom-theme-desc {
        font-size: 13px;
    }

    .map-color-picker {
        width: 50px;
        height: 35px;
    }



    .map-theme-button {
        min-height: 100px;
        padding: var(--map-space-3);
    }

    .map-theme-preview {
        width: 50px;
        height: 32px;
    }

    .map-theme-name {
        font-size: 12px;
    }

    .map-theme-check {
        width: 18px;
        height: 18px;
    }

    .map-theme-check svg {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 480px) {
    .map-contrast-themes-grid {
        grid-template-columns: 1fr;
        gap: var(--map-space-2);
    }

    .map-theme-button {
        flex-direction: row;
        min-height: 60px;
        padding: var(--map-space-3);
        gap: var(--map-space-3);
    }

    .map-theme-preview {
        width: 40px;
        height: 28px;
        flex-shrink: 0;
    }

    .map-theme-info {
        flex: 1;
        justify-content: space-between;
    }

    .map-theme-name {
        text-align: left;
        font-size: 13px;
    }
}

/* ===== TWO-LEVEL MODAL INTERFACE SYSTEM ===== */

/* Modal Views Container - Premium Animation System with Proper Scrolling */
.map-modal-view {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, opacity;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--map-gray-300) transparent;
}

.map-modal-view-active {
    display: block;
    opacity: 1;
    transform: translateX(0);
    z-index: 2;
}

/* Scrollbar styling for modal views */
.map-modal-view::-webkit-scrollbar {
    width: 6px;
}

.map-modal-view::-webkit-scrollbar-track {
    background: transparent;
}

.map-modal-view::-webkit-scrollbar-thumb {
    background: var(--map-gray-300);
    border-radius: var(--map-radius-full);
}

.map-modal-view::-webkit-scrollbar-thumb:hover {
    background: var(--map-gray-400);
}

/* Premium Forward Navigation Animation (Category → Options) */
.map-modal-view.map-view-entering-forward {
    display: block;
    opacity: 0;
    transform: translateX(100%) scale(0.95);
    animation: slideInForward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 3;
}

.map-modal-view.map-view-exiting-forward {
    opacity: 1;
    transform: translateX(0) scale(1);
    animation: slideOutForward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 1;
}

/* Premium Backward Navigation Animation (Options → Category) */
.map-modal-view.map-view-entering-backward {
    display: block;
    opacity: 0;
    transform: translateX(-100%) scale(0.95);
    animation: slideInBackward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 3;
}

.map-modal-view.map-view-exiting-backward {
    opacity: 1;
    transform: translateX(0) scale(1);
    animation: slideOutBackward 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    z-index: 1;
}

/* Forward Animation Keyframes */
@keyframes slideInForward {
    0% {
        opacity: 0;
        transform: translateX(100%) scale(0.95);
        filter: blur(2px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
}

@keyframes slideOutForward {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
    40% {
        opacity: 0.8;
        transform: translateX(-10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: translateX(-100%) scale(0.95);
        filter: blur(2px);
    }
}

/* Backward Animation Keyframes */
@keyframes slideInBackward {
    0% {
        opacity: 0;
        transform: translateX(-100%) scale(0.95);
        filter: blur(2px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(-10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
}

@keyframes slideOutBackward {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
        filter: blur(0);
    }
    40% {
        opacity: 0.8;
        transform: translateX(10%) scale(0.98);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: translateX(100%) scale(0.95);
        filter: blur(2px);
    }
}

/* Category Grid Layout */
.map-category-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--map-space-3);
    margin-top: var(--map-space-2);
}

/* ===== KEYBOARD SHORTCUTS PREMIUM STYLING ===== */

/* Shortcuts Grid Layout */
.map-shortcuts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--map-space-3);
    padding: var(--map-space-4);
    background: var(--map-gray-50);
    border-radius: var(--map-radius-lg);
    margin-top: var(--map-space-3);
}

/* Individual Shortcut Items */
.map-shortcut-item {
    display: flex;
    align-items: center;
    gap: var(--map-space-3);
    padding: var(--map-space-3);
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-md);
    transition: all var(--map-transition-base);
}

.map-shortcut-item:hover {
    border-color: var(--map-primary);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

/* Shortcut Key Styling */
.map-shortcut-key {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    padding: var(--map-space-2) var(--map-space-3);
    background: linear-gradient(135deg, var(--map-gray-100) 0%, var(--map-gray-200) 100%);
    border: 1px solid var(--map-gray-300);
    border-radius: var(--map-radius-md);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    color: var(--map-gray-700);
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.5);
    flex-shrink: 0;
}

/* Shortcut Description */
.map-shortcut-desc {
    flex: 1;
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-600);
    line-height: 1.5;
}

/* Expandable Content Animation - Enhanced for Proper Scrolling */
.map-expandable-content {
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Ensure expandable content doesn't get cut off */
.map-feature-section:last-child .map-expandable-content {
    margin-bottom: var(--map-space-6);
}

/* Responsive Design for Shortcuts */
@media (max-width: 480px) {
    .map-shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--map-space-2);
    }

    .map-shortcut-key {
        min-width: auto;
        align-self: flex-start;
    }
}



/* Category Buttons */
.map-category-button {
    display: flex;
    align-items: center;
    width: 100%;
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-4);
    cursor: pointer;
    transition: all var(--map-transition-base);
    color: var(--map-gray-700);
    text-align: left;
    position: relative;
    overflow: hidden;
    min-height: 72px;
}

.map-category-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-category-button:hover:not(.map-category-disabled) {
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-white) 100%);
    border-color: var(--map-primary);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.15), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.map-category-button:hover:not(.map-category-disabled)::before {
    opacity: 0.03;
}

.map-category-button:active:not(.map-category-disabled) {
    transform: translateY(-1px);
    box-shadow: var(--map-shadow-sm);
}

/* No default focus outline for category buttons */
.map-category-button:focus {
    outline: none;
}



/* Disabled category buttons */
.map-category-button.map-category-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--map-gray-50);
    border-color: var(--map-gray-200);
}

.map-category-button.map-category-disabled:hover {
    transform: none;
    box-shadow: none;
    background: var(--map-gray-50);
    border-color: var(--map-gray-200);
}

/* Premium Category Icons with Blue Gradient Scheme */
.map-category-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--map-radius-lg);
    margin-right: var(--map-space-4);
    flex-shrink: 0;
    transition: all var(--map-transition-base);
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

/* Individual Category Icon Colors - Premium Blue Variations */
.map-category-icon-text {
    background: linear-gradient(135deg, #6366f1 0%, #818cf8 100%);
    color: var(--map-white);
}

.map-category-icon-colors {
    background: linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%);
    color: var(--map-white);
}

.map-category-icon-navigation {
    background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
    color: var(--map-white);
}

.map-category-icon-preferences {
    background: linear-gradient(135deg, #3730a3 0%, #4f46e5 100%);
    color: var(--map-white);
}

/* Premium Hover Effects */
.map-category-button:hover:not(.map-category-disabled) .map-category-icon {
    transform: scale(1.08) translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-text {
    background: linear-gradient(135deg, #5b5bf6 0%, #7c7cfa 100%);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-colors {
    background: linear-gradient(135deg, #7c7cfa 0%, #a5a5fc 100%);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-navigation {
    background: linear-gradient(135deg, #4338ca 0%, #5b5bf6 100%);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-icon-preferences {
    background: linear-gradient(135deg, #312e81 0%, #4338ca 100%);
}

/* Category Content */
.map-category-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.map-category-title {
    margin: 0 0 var(--map-space-1) 0;
    font-size: var(--map-font-size-base);
    font-weight: 600;
    color: var(--map-gray-800);
    line-height: 1.4;
    transition: color var(--map-transition-base);
}

.map-category-desc {
    font-size: var(--map-font-size-sm);
    color: var(--map-gray-500);
    margin: 0;
    line-height: 1.5;
    transition: color var(--map-transition-base);
}

/* Premium hover text effects */
.map-category-button:hover:not(.map-category-disabled) .map-category-title {
    color: var(--map-gray-900);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-desc {
    color: var(--map-gray-600);
}

/* Premium Category Arrow - Enhanced Refined Style */
.map-category-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(99, 102, 241, 0.15);
    border-radius: var(--map-radius-lg);
    color: var(--map-gray-400);
    transition: all var(--map-transition-base), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.08);
    backdrop-filter: blur(4px);
}

.map-category-button:hover:not(.map-category-disabled) .map-category-arrow {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--map-primary);
    color: var(--map-primary);
    transform: translateX(4px) scale(1.08);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* Feature Toggle Arrow Hover Effects - Matching Premium Category Style */
.map-feature-toggle:hover .map-category-arrow {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--map-primary);
    color: var(--map-primary);
    transform: translateX(4px) scale(1.08);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* Active Feature Toggle Arrow Effects */
.map-feature-toggle.active .map-category-arrow,
.map-feature-toggle[data-active="true"] .map-category-arrow {
    background: rgba(99, 102, 241, 0.15);
    border-color: var(--map-primary);
    color: var(--map-primary);
    box-shadow: 0 3px 8px rgba(99, 102, 241, 0.25);
}

/* View Header */
.map-view-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--map-space-6);
    padding-bottom: var(--map-space-4);
    border-bottom: 1px solid var(--map-gray-200);
}

/* Premium Modal Back Button - Blue Gradient Style */
.map-back-button {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 1px solid var(--map-primary);
    border-radius: var(--map-radius-lg);
    padding: var(--map-space-3) var(--map-space-4);
    cursor: pointer;
    transition: all var(--map-transition-base);
    color: var(--map-white);
    font-size: var(--map-font-size-sm);
    font-weight: 600;
    margin-right: var(--map-space-4);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
    position: relative;
    overflow: hidden;
}

.map-back-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-back-button:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    color: var(--map-white);
    transform: translateY(-2px) translateX(-2px);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.25);
}

.map-back-button:hover::before {
    opacity: 1;
}

.map-back-button:active {
    transform: translateY(-1px) translateX(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

/* Default subtle focus for back button */
.map-back-button:focus {
    outline: 2px solid var(--map-gray-400);
    outline-offset: 1px;
}



.map-back-button svg {
    margin-right: var(--map-space-2);
    width: 16px;
    height: 16px;
}

.map-view-title {
    margin: 0;
    font-size: var(--map-font-size-lg);
    font-weight: 600;
    color: var(--map-gray-800);
    flex: 1;
}

/* View Content - Enhanced for Proper Scrolling with Maximum Bottom Space */
.map-view-content {
    padding: var(--map-space-6);
    padding-bottom: var(--map-space-12); /* Maximum bottom padding for generous space */
    min-height: 100%;
    box-sizing: border-box;
}





/* Placeholder Sections */
.map-placeholder-section {
    display: flex;
    align-items: flex-start;
    padding: var(--map-space-6);
    background: var(--map-gray-50);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    margin-bottom: var(--map-space-4);
}

.map-placeholder-icon {
    font-size: 32px;
    margin-right: var(--map-space-4);
    flex-shrink: 0;
    line-height: 1;
}

.map-placeholder-text {
    flex: 1;
}

.map-placeholder-text p {
    margin: 0 0 var(--map-space-3) 0;
    color: var(--map-gray-600);
    font-size: var(--map-font-size-sm);
    line-height: 1.6;
}

.map-placeholder-text ul {
    margin: 0;
    padding-left: var(--map-space-4);
    color: var(--map-gray-500);
    font-size: var(--map-font-size-sm);
}

.map-placeholder-text li {
    margin-bottom: var(--map-space-1);
    line-height: 1.5;
}

/* ===== RESPONSIVE DESIGN FOR MODAL SYSTEM ===== */

/* Large screens - Extra spacious */
@media (min-width: 1200px) {
    .map-widget-panel {
        width: 520px; /* Even larger on big screens */
        height: 750px; /* Maximum height for large screens */
    }

    /* Better positioning on large screens */
    .map-position-bottom-right .map-widget-panel,
    .map-position-bottom-left .map-widget-panel {
        bottom: -10px; /* Moved up more for large screens */
    }

    .map-position-top-right .map-widget-panel,
    .map-position-top-left .map-widget-panel {
        margin-top: 15px; /* Reduced margin for closer positioning */
    }

    /* Move right panels optimally on large screens */
    .map-position-bottom-right .map-widget-panel,
    .map-position-top-right .map-widget-panel {
        right: -15px; /* Optimal positioning to show all corners */
    }
}

/* Tablet and smaller screens */
@media (max-width: 768px) {
    .map-widget-panel {
        width: 450px; /* Increased from 400px */
        height: 650px; /* Significantly increased for maximum bottom space */
    }

    .map-panel-content {
        height: calc(650px - 80px);
    }

    .map-modal-views-container {
        height: calc(650px - 80px);
    }

    .map-category-button {
        padding: var(--map-space-3);
        min-height: 64px;
    }

    .map-category-icon {
        width: 40px;
        height: 40px;
        margin-right: var(--map-space-3);
    }

    /* Emoji styling removed - now using premium SVG icons */

    .map-view-header {
        margin-bottom: var(--map-space-4);
        padding-bottom: var(--map-space-3);
    }

    .map-back-button {
        padding: var(--map-space-2);
        margin-right: var(--map-space-3);
    }

    .map-view-title {
        font-size: var(--map-font-size-base);
    }
}

/* Mobile screens */
@media (max-width: 480px) {
    .map-widget-panel {
        width: 380px; /* Increased from 360px */
        height: 600px; /* Significantly increased for maximum bottom space */
    }

    .map-panel-content {
        height: calc(600px - 70px);
        padding: var(--map-space-4);
    }

    .map-modal-views-container {
        height: calc(600px - 70px);
    }

    .map-category-grid {
        gap: var(--map-space-2);
    }

    .map-category-button {
        padding: var(--map-space-3);
        min-height: 60px;
    }

    .map-category-icon {
        width: 36px;
        height: 36px;
        margin-right: var(--map-space-3);
    }

    /* Emoji styling removed - now using premium SVG icons */

    .map-category-title {
        font-size: var(--map-font-size-sm);
    }

    .map-category-desc {
        font-size: 12px;
    }

    .map-back-button {
        padding: var(--map-space-1) var(--map-space-2);
        font-size: 12px;
    }

    .map-back-button svg {
        width: 14px;
        height: 14px;
    }

    .map-view-title {
        font-size: var(--map-font-size-sm);
    }

    .map-placeholder-section {
        padding: var(--map-space-4);
    }

    .map-placeholder-icon {
        font-size: 24px;
        margin-right: var(--map-space-3);
    }

    /* Center position adjustments for mobile */
    .map-position-center {
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        transform: none;
    }

    .map-position-center .map-widget-panel {
        width: 100%;
        left: 0;
        transform: none;
        max-height: calc(100vh - 100px);
        height: auto; /* Allow flexible height for very small screens */
    }
}

/* High contrast mode enhancements */
@media (prefers-contrast: high) {
    .map-category-button {
        border-width: 2px;
    }

    .map-category-button:hover:not(.map-category-disabled) {
        border-width: 3px;
    }

    .map-back-button {
        border-width: 2px;
    }

    .map-placeholder-section {
        border-width: 2px;
    }
}

/* Reduced motion for modal transitions */
@media (prefers-reduced-motion: reduce) {
    .map-modal-view,
    .map-modal-view.map-view-entering,
    .map-modal-view.map-view-entered,
    .map-modal-view.map-view-exiting {
        transition: none !important;
        transform: none !important;
    }

    .map-category-button:hover:not(.map-category-disabled),
    .map-back-button:hover {
        transform: none !important;
    }
}

/* ===== DYSLEXIC FONT FEATURE ===== */

/**
 * Dyslexic Font Class
 * Applied to body when dyslexic font is enabled
 * Uses OpenDyslexic font with fallbacks
 */
.dyslexic-font {
    font-family: 'OpenDyslexic', 'OpenDyslexic-Regular', Arial, sans-serif !important;
}

.dyslexic-font * {
    font-family: inherit !important;
}

/* Ensure proper font loading and fallback */
.dyslexic-font h1,
.dyslexic-font h2,
.dyslexic-font h3,
.dyslexic-font h4,
.dyslexic-font h5,
.dyslexic-font h6 {
    font-family: 'OpenDyslexic', 'OpenDyslexic-Bold', Arial, sans-serif !important;
    font-weight: bold;
}

/* Preserve accessibility widget styling when dyslexic font is active */
.dyslexic-font .map-accessibility-widget,
.dyslexic-font .map-accessibility-widget * {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
}

/* Loading state for font */
.dyslexic-font-loading {
    font-display: swap;
}

/* Smooth transition when applying/removing dyslexic font */
body {
    transition: font-family 0.3s ease-in-out;
}

/* Ensure readability improvements */
.dyslexic-font {
    line-height: 1.6 !important;
    letter-spacing: 0.05em !important;
    word-spacing: 0.1em !important;
}

/* Preserve form elements styling */
.dyslexic-font input,
.dyslexic-font textarea,
.dyslexic-font select,
.dyslexic-font button {
    font-family: 'OpenDyslexic', Arial, sans-serif !important;
}

/* Status message styling removed for cleaner UX */

/* ===== READING GUIDE FEATURE ===== */

/**
 * Reading Guide Line
 * A horizontal line that follows the mouse cursor to help users focus on text
 */
.map-reading-guide {
    position: fixed;
    left: 0;
    right: 0;
    width: 100%;
    height: 3px;
    background: rgba(99, 102, 241, 0.6);
    box-shadow: 0 0 8px rgba(99, 102, 241, 0.4);
    z-index: var(--map-z-tooltip);
    pointer-events: none;
    transition: opacity 0.2s ease-in-out;
    border-radius: 1px;
}

/* Alternative styles for different themes */
.map-theme-ocean .map-reading-guide {
    background: rgba(14, 165, 233, 0.6);
    box-shadow: 0 0 8px rgba(14, 165, 233, 0.4);
}

.map-theme-forest .map-reading-guide {
    background: rgba(5, 150, 105, 0.6);
    box-shadow: 0 0 8px rgba(5, 150, 105, 0.4);
}

/* Smooth animation for guide line movement */
.map-reading-guide.smooth {
    transition: top 0.1s ease-out, opacity 0.2s ease-in-out;
}

/* Hide guide line when not active */
.map-reading-guide.hidden {
    opacity: 0;
    visibility: hidden;
}

/* Ensure guide line doesn't interfere with text selection */
.map-reading-guide {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Status message styling removed for cleaner UX */

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
    .map-reading-guide {
        height: 4px; /* Slightly thicker on mobile for better visibility */
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .map-reading-guide {
        background: rgba(0, 0, 0, 0.8);
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.6);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .map-reading-guide,
    .map-reading-guide.smooth {
        transition: opacity 0.2s ease-in-out;
    }
}



/* ===== FONT SIZE CONTROL FEATURE ===== */

/**
 * Font Size Control Section
 * Clean, organized layout for font size adjustment controls
 */

/* Compact Font Size Controls */
.map-font-size-controls {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
}

.map-size-control-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--map-space-3);
    padding: var(--map-space-3);
    background: var(--map-white);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.map-size-control-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 50px;
    background: linear-gradient(135deg, var(--map-gray-50) 0%, var(--map-gray-100) 100%);
    border: 2px solid var(--map-gray-200);
    border-radius: var(--map-radius-md);
    cursor: pointer;
    transition: all var(--map-transition-base);
    color: var(--map-gray-600);
    font-size: 10px;
    font-weight: 500;
    gap: 2px;
}

.map-size-control-btn:hover {
    background: linear-gradient(135deg, var(--map-primary-light) 0%, var(--map-primary) 100%);
    border-color: var(--map-primary);
    color: var(--map-white);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--map-primary-rgb), 0.3);
}

.map-size-control-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(var(--map-primary-rgb), 0.2);
}

.map-size-control-btn svg {
    transition: all var(--map-transition-base);
}

.map-size-label {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.map-size-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: var(--map-space-2);
    background: linear-gradient(135deg, rgba(var(--map-primary-rgb), 0.05) 0%, rgba(var(--map-primary-rgb), 0.1) 100%);
    border: 1px solid rgba(var(--map-primary-rgb), 0.2);
    border-radius: var(--map-radius-md);
    min-width: 80px;
}

.map-size-preview {
    font-size: 20px;
    font-weight: 700;
    color: var(--map-primary);
    line-height: 1;
    transition: font-size var(--map-transition-base);
}

.map-size-percentage {
    font-size: 10px;
    font-weight: 600;
    color: var(--map-gray-600);
    background: var(--map-white);
    padding: 1px 6px;
    border-radius: var(--map-radius-sm);
    border: 1px solid var(--map-gray-200);
}

.map-font-size-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 12px;
    background: var(--map-surface);
    border: 2px solid var(--map-border);
    border-radius: 6px;
    color: var(--map-text);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 50px;
    height: 36px;
    flex: 1;
}

.map-font-size-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-font-size-btn:active {
    transform: translateY(0);
}

.map-font-size-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.map-font-size-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Font size button icons */
.map-font-size-btn svg {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

/* Specific button styling */
.map-font-size-decrease {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #dc2626;
}

.map-font-size-decrease:hover:not(:disabled) {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
}

.map-font-size-increase {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: #059669;
}

.map-font-size-increase:hover:not(:disabled) {
    background: #059669;
    color: white;
    border-color: #059669;
}

/* Premium Font Size Reset Button - Blue Gradient Style */
.map-font-size-reset {
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 2px solid var(--map-primary);
    color: var(--map-white);
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
    position: relative;
    overflow: hidden;
}

.map-font-size-reset::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-font-size-reset:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    color: var(--map-white);
    border-color: var(--map-primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(99, 102, 241, 0.25);
}

.map-font-size-reset:hover:not(:disabled)::before {
    opacity: 1;
}



/* ===== FONT SIZE SCALING SYSTEM ===== */

/**
 * Font Size Scaling System
 * Uses dynamic CSS generation for flexible font size adjustments
 * The actual font size rules are generated dynamically by JavaScript
 * and injected as a <style> element with id="map-font-size-style"
 */

/* Base class applied to html when font size is active */
.map-font-size-active {
    /* This class is used as a selector for dynamic CSS rules */
}

/* ===== LINE SPACING CONTROL FEATURE ===== */

/**
 * Line Spacing Control Section
 * Clean, organized layout for line spacing adjustment controls
 */



/* Compact Line Spacing Controls */
.map-line-spacing-controls {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
}

.map-spacing-control-group {
    display: flex;
    flex-direction: column;
    gap: var(--map-space-3);
    padding: var(--map-space-4);
    background: linear-gradient(135deg, var(--map-white) 0%, rgba(248, 250, 252, 0.8) 100%);
    border: 1px solid var(--map-gray-200);
    border-radius: var(--map-radius-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.map-spacing-control-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(var(--map-primary-rgb), 0.2) 50%, transparent 100%);
}

.map-spacing-labels {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--map-space-3);
    padding: 0 4px;
}

.map-spacing-label-min,
.map-spacing-label-center,
.map-spacing-label-max {
    font-size: 10px;
    font-weight: 700;
    color: var(--map-gray-500);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    position: relative;
}

.map-spacing-label-center {
    color: var(--map-primary);
    background: linear-gradient(135deg, rgba(var(--map-primary-rgb), 0.1) 0%, rgba(var(--map-primary-rgb), 0.05) 100%);
    padding: 2px 8px;
    border-radius: var(--map-radius-sm);
    border: 1px solid rgba(var(--map-primary-rgb), 0.2);
}

/* Premium Slider Styling */
.map-slider-container {
    position: relative;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
}

.map-premium-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 6px;
    background: transparent;
    outline: none;
    border-radius: 3px;
    position: relative;
    z-index: 2;
    cursor: pointer;
}

.map-slider-track {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, var(--map-gray-200) 0%, var(--map-gray-300) 100%);
    border-radius: 3px;
    transform: translateY(-50%);
    z-index: 1;
}

.map-slider-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border-radius: 3px;
    transition: width var(--map-transition-base);
    width: 33.33%; /* Default position for 1.5 value */
}

/* Webkit Slider Thumb */
.map-premium-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-50) 100%);
    border: 3px solid var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(var(--map-primary-rgb), 0.3);
    transition: all var(--map-transition-base);
}

.map-premium-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(var(--map-primary-rgb), 0.4);
}

/* Firefox Slider Thumb */
.map-premium-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, var(--map-white) 0%, var(--map-gray-50) 100%);
    border: 3px solid var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(var(--map-primary-rgb), 0.3);
    transition: all var(--map-transition-base);
}

.map-premium-slider::-moz-range-track {
    background: transparent;
    border: none;
}

.map-spacing-value {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: var(--map-space-3);
}

.map-spacing-value span {
    font-size: var(--map-font-size-sm);
    font-weight: 700;
    color: var(--map-white);
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    padding: 6px 16px;
    border-radius: var(--map-radius-lg);
    border: 1px solid rgba(var(--map-primary-rgb), 0.3);
    box-shadow: 0 2px 6px rgba(var(--map-primary-rgb), 0.25);
    min-width: 50px;
    text-align: center;
    letter-spacing: 0.5px;
}

/* Premium Control Reset Button - Blue Gradient Style */
.map-control-reset {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 10px var(--map-space-4);
    margin-top: var(--map-space-3);
    margin-bottom: var(--map-space-4);
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 1px solid var(--map-primary);
    border-radius: var(--map-radius-lg);
    color: var(--map-white);
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--map-transition-base);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(8px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
}

/* Premium Control Reset Button Effects */
.map-control-reset::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-control-reset:hover {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    border-color: var(--map-primary-dark);
    color: var(--map-white);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.25);
}

.map-control-reset:hover::before {
    opacity: 1;
}

.map-control-reset:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.map-control-reset svg {
    transition: all var(--map-transition-base);
    width: 14px;
    height: 14px;
}

.map-control-reset:hover svg {
    transform: rotate(180deg) scale(1.1);
}

.map-line-spacing-slider-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.map-slider-container {
    flex: 1;
    position: relative;
}

.map-spacing-label-min,
.map-spacing-label-max {
    font-size: 11px;
    color: var(--map-text-secondary);
    font-weight: 500;
    min-width: 30px;
    text-align: center;
    line-height: 1;
}

.map-line-spacing-slider {
    width: 100%;
    height: 4px;
    background: var(--map-border);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
    position: relative;
}

.map-line-spacing-slider:focus {
    outline: 2px solid var(--map-primary);
    outline-offset: 2px;
}

.map-line-spacing-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border: 2px solid white;
}

.map-line-spacing-slider::-webkit-slider-thumb:hover {
    background: var(--map-primary-dark);
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

.map-line-spacing-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--map-primary);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.map-line-spacing-slider::-moz-range-thumb:hover {
    background: var(--map-primary-dark);
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

.map-line-spacing-slider::-moz-range-track {
    height: 4px;
    background: var(--map-border);
    border-radius: 2px;
    border: none;
}

/* Premium Line Spacing Reset Button - Blue Gradient Style */
.map-line-spacing-reset-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 6px 10px;
    background: linear-gradient(135deg, var(--map-primary) 0%, var(--map-primary-light) 100%);
    border: 2px solid var(--map-primary);
    border-radius: var(--map-radius-md);
    color: var(--map-white);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--map-transition-base);
    height: 28px;
    min-width: 60px;
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.15);
    position: relative;
    overflow: hidden;
}

.map-line-spacing-reset-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity var(--map-transition-base);
}

.map-line-spacing-reset-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--map-primary-dark) 0%, var(--map-primary) 100%);
    color: var(--map-white);
    border-color: var(--map-primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(99, 102, 241, 0.25);
}

.map-line-spacing-reset-btn:hover:not(:disabled)::before {
    opacity: 1;
}

.map-line-spacing-reset-btn:active {
    transform: translateY(0);
}

.map-line-spacing-reset-btn svg {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}



/* Line spacing scaling system */
.map-line-spacing-active {
    /* This class is used as a selector for dynamic CSS rules */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .map-feature-controls {
        padding: var(--map-space-4);
    }

    .map-control-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--map-space-2);
    }

    .map-control-value {
        align-self: flex-end;
        min-width: 70px;
    }

    .map-size-control-group {
        flex-direction: column;
        gap: var(--map-space-2);
    }

    .map-size-control-btn {
        width: 100%;
        height: 40px;
        flex-direction: row;
        gap: 6px;
    }

    .map-size-indicator {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        padding: 8px;
    }

    .map-spacing-control-group {
        gap: var(--map-space-2);
    }

    .map-premium-slider::-webkit-slider-thumb {
        width: 18px;
        height: 18px;
    }

    .map-premium-slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
    }

    .map-line-spacing-controls {
        gap: 6px;
    }

    .map-line-spacing-slider-wrapper {
        gap: 8px;
    }

    .map-line-spacing-reset-btn {
        min-width: 50px;
        padding: 5px 8px;
        font-size: 11px;
        height: 26px;
    }

    .map-line-spacing-reset-btn svg {
        width: 10px;
        height: 10px;
    }

    .map-spacing-label-min,
    .map-spacing-label-max {
        font-size: 10px;
        min-width: 25px;
    }

    .map-line-spacing-slider::-webkit-slider-thumb {
        width: 14px;
        height: 14px;
    }

    .map-line-spacing-slider::-moz-range-thumb {
        width: 14px;
        height: 14px;
    }
}

/* ===== ADHD FOCUS MODE ===== */
body.map-adhd-focus-mode {
    position: relative;
}

/* ADHD Focus Mode Overlay */
#map-adhd-focus-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2147483647 !important;
    pointer-events: none;
    display: none;
    transition: none;
    /* Ensure no filters affect the content */
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
}

/* Premium ADHD Focus Mode Border */
#map-adhd-focus-border {
    position: fixed;
    left: 0;
    width: 100%;
    border-top: 3px solid var(--map-primary);
    border-bottom: 3px solid var(--map-primary);
    border-left: none;
    border-right: none;
    z-index: 2147483648 !important;
    pointer-events: none;
    display: none;
    transition: none;

    /* Premium styling with enhanced ADHD focus glow */
    box-shadow:
        /* Outer glow effects - extending into dark overlay */
        0 -15px 30px rgba(99, 102, 241, 0.4),
        0 15px 30px rgba(99, 102, 241, 0.4),
        0 -25px 50px rgba(99, 102, 241, 0.25),
        0 25px 50px rgba(99, 102, 241, 0.25),
        0 -35px 70px rgba(99, 102, 241, 0.15),
        0 35px 70px rgba(99, 102, 241, 0.15),
        /* Subtle inner highlight */
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);

    /* 100% transparent background */
    background: transparent;
}

/* Enhanced border for high contrast mode */
@media (prefers-contrast: high) {
    #map-adhd-focus-border {
        border-top-width: 4px;
        border-bottom-width: 4px;
        border-top-color: #000000;
        border-bottom-color: #000000;
        box-shadow:
            0 0 0 2px #ffffff,
            0 0 0 6px #000000;
        background: transparent;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    #map-adhd-focus-border {
        transition: none;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
}

/* Theme-aware border colors */
.map-theme-ocean #map-adhd-focus-border {
    border-top-color: #0ea5e9;
    border-bottom-color: #0ea5e9;
    box-shadow:
        /* Ocean theme ADHD focus glow */
        0 -15px 30px rgba(14, 165, 233, 0.4),
        0 15px 30px rgba(14, 165, 233, 0.4),
        0 -25px 50px rgba(14, 165, 233, 0.25),
        0 25px 50px rgba(14, 165, 233, 0.25),
        0 -35px 70px rgba(14, 165, 233, 0.15),
        0 35px 70px rgba(14, 165, 233, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

.map-theme-forest #map-adhd-focus-border {
    border-top-color: #059669;
    border-bottom-color: #059669;
    box-shadow:
        /* Forest theme ADHD focus glow */
        0 -15px 30px rgba(5, 150, 105, 0.4),
        0 15px 30px rgba(5, 150, 105, 0.4),
        0 -25px 50px rgba(5, 150, 105, 0.25),
        0 25px 50px rgba(5, 150, 105, 0.25),
        0 -35px 70px rgba(5, 150, 105, 0.15),
        0 35px 70px rgba(5, 150, 105, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

.map-theme-sunset #map-adhd-focus-border {
    border-top-color: #ea580c;
    border-bottom-color: #ea580c;
    box-shadow:
        /* Sunset theme ADHD focus glow */
        0 -15px 30px rgba(234, 88, 12, 0.4),
        0 15px 30px rgba(234, 88, 12, 0.4),
        0 -25px 50px rgba(234, 88, 12, 0.25),
        0 25px 50px rgba(234, 88, 12, 0.25),
        0 -35px 70px rgba(234, 88, 12, 0.15),
        0 35px 70px rgba(234, 88, 12, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

.map-theme-purple #map-adhd-focus-border {
    border-top-color: #7c3aed;
    border-bottom-color: #7c3aed;
    box-shadow:
        /* Purple theme ADHD focus glow */
        0 -15px 30px rgba(124, 58, 237, 0.4),
        0 15px 30px rgba(124, 58, 237, 0.4),
        0 -25px 50px rgba(124, 58, 237, 0.25),
        0 25px 50px rgba(124, 58, 237, 0.25),
        0 -35px 70px rgba(124, 58, 237, 0.15),
        0 35px 70px rgba(124, 58, 237, 0.15),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    background: transparent;
}

/* Force accessibility widget to be below ADHD focus overlay */
body.map-adhd-focus-mode .map-accessibility-widget,
body.map-adhd-focus-mode .map-accessibility-widget *,
body.map-adhd-focus-mode .map-widget-panel,
body.map-adhd-focus-mode .map-main-toggle {
    z-index: 1000 !important;
}

/* ===== BIG CURSOR MODE ===== */
body.map-big-cursor-mode,
body.map-big-cursor-mode * {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="black" stroke="white" stroke-width="1"><path d="M3 3v18l6-6h6l-6-6V3z"/></svg>') 4 4, auto !important;
}

/* Big cursor for interactive elements */
body.map-big-cursor-mode a,
body.map-big-cursor-mode button,
body.map-big-cursor-mode input[type="button"],
body.map-big-cursor-mode input[type="submit"],
body.map-big-cursor-mode select,
body.map-big-cursor-mode [role="button"],
body.map-big-cursor-mode .clickable {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="black" stroke="white" stroke-width="1"><path d="M9 9h6v6h-6z"/></svg>') 18 18, pointer !important;
}

/* Big cursor for text input fields */
body.map-big-cursor-mode input[type="text"],
body.map-big-cursor-mode input[type="email"],
body.map-big-cursor-mode input[type="password"],
body.map-big-cursor-mode input[type="search"],
body.map-big-cursor-mode input[type="url"],
body.map-big-cursor-mode textarea,
body.map-big-cursor-mode [contenteditable] {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="black" stroke="white" stroke-width="1"><path d="M12 2v20M8 6h8M8 18h8"/></svg>') 16 16, text !important;
}

/* Big cursor for resize handles */
body.map-big-cursor-mode [resize],
body.map-big-cursor-mode .resize-handle {
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="black" stroke="white" stroke-width="1"><path d="M16 8l4 4-4 4M8 8l-4 4 4 4"/></svg>') 18 18, ew-resize !important;
}
