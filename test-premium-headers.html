<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Headers Test - Accessibility Plugin</title>
    <link rel="stylesheet" href="assets/css/frontend.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
            background: #f8fafc;
            padding: 40px 20px;
            margin: 0;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .test-title {
            text-align: center;
            color: #1f2937;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 700;
        }
        .section-spacing {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎨 Premium Headers Showcase</h1>
        
        <!-- Visual Themes Section -->
        <div class="map-feature-section map-feature-bordered section-spacing">
            <div class="map-contrast-themes-header">
                <button class="map-collapsible-header" type="button" aria-expanded="false">
                    <div class="map-contrast-themes-info">
                        <div class="map-contrast-themes-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 18.5A6.5 6.5 0 1 1 18.5 12 6.51 6.51 0 0 1 12 18.5zm0-11A4.5 4.5 0 1 0 16.5 12 4.51 4.51 0 0 0 12 7.5z"/>
                                <path d="M12 1a1 1 0 0 1 1 1v3a1 1 0 0 1-2 0V2a1 1 0 0 1 1-1zM21 11h-3a1 1 0 0 0 0 2h3a1 1 0 0 0 0-2zM12 21a1 1 0 0 1 1 1v1a1 1 0 0 1-2 0v-1a1 1 0 0 1 1-1zM3.22 6.22a1 1 0 0 1 1.41 0L6.34 7.93a1 1 0 1 1-1.41 1.41L3.22 7.63a1 1 0 0 1 0-1.41zM17.66 16.07a1 1 0 0 1 1.41 1.41l1.71 1.71a1 1 0 1 1-1.41 1.41l-1.71-1.71a1 1 0 0 1 0-1.41z"/>
                            </svg>
                        </div>
                        <div class="map-contrast-themes-text">
                            <div class="map-contrast-themes-title">Visual Themes</div>
                            <div class="map-contrast-themes-desc">Choose a visual style that works best for you</div>
                        </div>
                    </div>
                    <div class="map-contrast-themes-current">Default</div>
                    <div class="map-contrast-themes-arrow">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                        </svg>
                    </div>
                </button>
            </div>
        </div>

        <!-- Color Studio Section -->
        <div class="map-feature-section map-feature-bordered map-color-studio section-spacing">
            <div class="map-custom-theme-header">
                <button class="map-collapsible-header map-studio-header" type="button" aria-expanded="false">
                    <div class="map-custom-theme-info">
                        <div class="map-custom-theme-icon map-studio-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="13.5" cy="6.5" r=".5"/>
                                <circle cx="17.5" cy="10.5" r=".5"/>
                                <circle cx="8.5" cy="7.5" r=".5"/>
                                <circle cx="6.5" cy="12.5" r=".5"/>
                                <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
                            </svg>
                        </div>
                        <div class="map-custom-theme-text">
                            <div class="map-custom-theme-title">Color Studio</div>
                            <div class="map-custom-theme-desc">Design your perfect color palette with live preview</div>
                        </div>
                    </div>
                    <div class="map-custom-theme-arrow map-studio-arrow">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m9 18 6-6-6-6"/>
                        </svg>
                    </div>
                </button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #6b7280; font-size: 14px;">
            <p>✨ <strong>Clean & Simple Design</strong> - Matching Text Options Style</p>
            <p>🎯 Hover over the sections to see the subtle gradient overlay</p>
        </div>
    </div>

    <script>
        // Add some basic interactivity for testing
        document.querySelectorAll('.map-collapsible-header').forEach(button => {
            button.addEventListener('click', function() {
                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                this.setAttribute('aria-expanded', !isExpanded);
            });
        });
    </script>
</body>
</html>
